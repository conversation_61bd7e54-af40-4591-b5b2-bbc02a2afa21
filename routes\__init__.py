from routes.index import index_router
from routes.ocr import ocr_router
from routes.frequency import frequency_router
from routes.qa import qa_router
from routes.summary import summary_router
from routes.frequency_and_summary import frequency_and_summary_router
from routes.extract_fields import extract_pdf_fields_router
from routes.parse_fields import parse_html_fields_router
from routes.ocr_and_embeddings import ocr_and_embeddings_router
from routes.vob_summary import vob_summary_router
from routes.embed_query import embed_query_router
from routes.document_categorization import document_categorization_router

routers = [
    index_router,
    document_categorization_router,
    extract_pdf_fields_router,
    qa_router,
    summary_router,
    frequency_router,
    vob_summary_router,
    frequency_and_summary_router,
    parse_html_fields_router,
    embed_query_router,
    ocr_router,
    ocr_and_embeddings_router,
]
