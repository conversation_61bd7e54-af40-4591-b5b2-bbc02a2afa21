import json
import logging
import os
from typing import Any, Dict, List
from langchain_aws import BedrockEmbeddings

class CustomBedrockEmbeddings(BedrockEmbeddings):

    def _embedding_func(self, text: str) -> Dict[str, Any]:
        """
        Call out to Bedrock embedding endpoint and return embeddings with usage details.
        Args:
            text (str): The text to embed.
        Returns:
            Dict[str, Any]: The embeddings, model, and usage details.
        """
        # replace newlines, which can negatively affect performance.
        text = text.replace(os.linesep, " ")

        # format input body for provider
        provider = self.model_id.split(".")[0]
        input_body: Dict[str, Any] = {}
        if provider == "cohere":
            input_body["input_type"] = "search_document"
            input_body["texts"] = [text]
        else:
            # includes common provider == "amazon"
            input_body["inputText"] = text

        if self.model_kwargs:
            input_body = {**input_body, **self.model_kwargs}

        body = json.dumps(input_body)

        try:
            response = self.client.invoke_model(
                body=body,
                modelId=self.model_id,
                accept="application/json",
                contentType="application/json",
            )

            # Read the response body and parse the JSON
            response_body = json.loads(response.get("body").read())
            
            # Extract the embedding and token count
            if provider == "cohere":
                embedding = response_body.get("embeddings")[0]
                token_count = 0
            else:
                embedding = response_body.get("embedding")
                token_count = response_body.get("inputTextTokenCount", 0)

            # Prepare the final response with embedding, model ID, and usage details
            result = {
                "embeddings": [embedding],
                "model": self.model_id,
                "usage": {
                    "prompt_tokens": token_count,
                    "total_tokens": token_count
                }
            }

            return result

        except Exception as e:
            logging.exception(f"Error raised by inference endpoint: {e}")
            raise e

    def embed_documents(self, texts: List[str]) -> Dict[str, Any]:
        """Compute doc embeddings using a Bedrock model and return the combined response format.

        Args:
            texts: The list of texts to embed.

        Returns:
            A single response containing all embeddings, model, and summed usage tokens.
        """
        combined_embeddings = []
        total_prompt_tokens = 0
        total_total_tokens = 0
        model_id = self.model_id

        for text in texts:
            response = self._embedding_func(text)

            # Normalize the embedding if needed
            if self.normalize:
                response["embeddings"][0] = self._normalize_vector(response["embeddings"][0])

            # Add the embeddings to the combined list
            combined_embeddings.extend(response["embeddings"])

            # Sum the tokens
            total_prompt_tokens += response["usage"]["prompt_tokens"]
            total_total_tokens += response["usage"]["total_tokens"]

        # Return a single combined response
        combined_response = {
            "embeddings": combined_embeddings,
            "model": model_id,
            "usage": {
                "prompt_tokens": total_prompt_tokens,
                "total_tokens": total_total_tokens
            }
        }

        return combined_response


    def embed_query(self, text: str) -> Dict[str, Any]:
        """Compute query embeddings using a Bedrock model and return the extended response format.

        Args:
            text: The text to embed.

        Returns:
            Embeddings with model and usage details for the query.
        """
        response = self._embedding_func(text)

        if self.normalize:
            response["embeddings"][0] = self._normalize_vector(response["embeddings"][0])

        return response