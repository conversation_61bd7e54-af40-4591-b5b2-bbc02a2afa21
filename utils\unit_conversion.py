import re
import logging
from typing import Dict, Any


def convert_unit_of_measurement(
    response: Dict[str, str], conversion_map: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Convert units for various data points based on the given conversion map.
    If no unit is found, the original value is returned.
    Args:
        response (Dict[str, str]): A dictionary containing values with units that need conversion.
        conversion_map (Dict[str, Any]): A mapping that defines how specific units should be converted.
    Returns:
        Dict[str, Any]: A new dictionary where values have been converted according to the conversion map.
    """
    unit_mapping = {
        "ft": 0.3048,
        "feet": 0.3048,
        "foot": 0.3048,
        "in": 0.0254,
        "inch": 0.0254,
        "inches": 0.0254,
        "cm": 0.01,
        "centimeter": 0.01,
        "centimeters": 0.01,
        "m": 1,
        "meter": 1,
        "meters": 1,
        "metre": 1,
        "metres": 1,
        "kg": 1,
        "kilogram": 1,
        "kilograms": 1,
        "g": 0.001,
        "gram": 0.001,
        "grams": 0.001,
        "oz": 0.0283495,
        "ounce": 0.0283495,
        "ounces": 0.0283495,
        "lb": 0.453592,
        "lbs": 0.453592,
        "pound": 0.453592,
        "pounds": 0.453592,
    }
    target_conversion = {
        "m": 1,
        "cm": 100,
        "ft": 3.28084,
        "in": 39.3701,
        "kg": 1,
        "g": 1000,
        "oz": 35.274,
        "lbs": 2.20462,
    }

    converted_response = {
        key: value
        for key, value in response.items()
        if key not in {details["data_point"] for details in conversion_map.values()}
    }
    for key, details in conversion_map.items():
        data_point, target_unit = details["data_point"], details["desired_unit"]
        value = response.get(key)
        if not value:
            continue

        value = re.split(r"\(", value)[0].strip()

        # Extract values and units
        matches = re.findall(r"(\d+\.?\d*)\s*(\w+)?", value)
        total_value = 0
        found_unit = False

        for v, u in matches:
            if u and u in unit_mapping:
                total_value += float(v) * unit_mapping[u]
                found_unit = True
            elif not u:
                logging.warning(
                    f"No units received by LLM for data point: '{data_point}'"
                )
            else:
                logging.warning(
                    f"Unrecognized unit '{u}' in data point: '{data_point}'"
                )

        if not found_unit:
            converted_response[data_point] = value
        else:
            if target_unit not in target_conversion:
                raise ValueError(
                    f"Unsupported target unit: {target_unit}. Supported targeted units are: m,cm,ft,in,kg,g,oz,lbs"
                )
            converted_response[data_point] = (
                total_value * target_conversion[target_unit]
            )

    return converted_response
