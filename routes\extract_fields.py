from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_EXTRACT_PDF_FIELDS,
    OCR_PLATFORM_EXTRACT_PDF_FIELDS,
    EMBEDDING_PLATFORM_EXTRACT_PDF_FIELDS,
    RAG_ENABLED,
    LLM_REASONING_ENABLED_EXTRACT_PDF_FIELDS,
)
from services.extract_fields_service import (
    extract_data_points_with_context,
)
from utils.document_utils import SourceDocument
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from utils.enums import (
    OCRType,
    CloudPlatform,
    ExtractionType,
    EndpointDescriptions,
    Questions,
)
from utils.input_utils import validate_user_questions
from utils.page_range_utils import get_list_of_pages
from utils.cloud_utils import (
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_embedding_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(
    OCR_PLATFORM_EXTRACT_PDF_FIELDS, "/extract-pdf-fields"
)
LLM_PLATFORM = get_endpoint_llm_platform(
    LLM_PLATFORM_EXTRACT_PDF_FIELDS, "/extract-pdf-fields"
)
EMBEDDING_PLATFORM = get_endpoint_embedding_platform(
    EMBEDDING_PLATFORM_EXTRACT_PDF_FIELDS, "/extract-pdf-fields"
)
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    LLM_REASONING_ENABLED_EXTRACT_PDF_FIELDS, "/extract-pdf-fields"
)

extract_pdf_fields_router = APIRouter()


@extract_pdf_fields_router.post(
    "/extractFields",
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.extractField,
)
@extract_pdf_fields_router.post(
    "/extract-pdf-fields",
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.extractField,
)
async def extract_pdf_fields(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    queries: str = Form(None, description="Comma separated query fields."),
    data_points: Questions = Form(
        None,
        description="List of data points for the AI to extract.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    embedding_platform: CloudPlatform = Form(
        EMBEDDING_PLATFORM,
        description=f"Optional: Embedding Platform to use, could be 'azure' or 'aws'. Default is {EMBEDDING_PLATFORM}.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    extraction_type: ExtractionType = Form(
        ExtractionType.AllField,
        description="Optional: Extraction Type, could be 'af' or 'sf'. By default af.",
    ),
    page_range: str = Form(
        None,
        description="Optional: Page range for PDF pages to use. Example: 1,3,5-10 for pages [1,3,5,6,7,8,9,10]. Default is None.",
    ),
    rag_enabled: str = Form(
        RAG_ENABLED,
        enum=["true", "false"],
        description=f"Optional: Specifies whether to use RAG or skip it, the values could be true or false. Default is {RAG_ENABLED}",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    Extract fields from the PDF document.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        queries (str, optional): The comma separated query fields.
        data_points (Questions, optional): The list of data points for the AI to extract.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        llm_platform (CloudPlatform, optional): The LLM platform to use.
        embedding_platform (CloudPlatform, optional): The embedding platform to use.
        extraction_type (ExtractionType, optional): The extraction type.
        ocr_type (OCRType, optional): The OCR type.
        page_range (str, optional): Page range for PDF pages to use.
        rag_enabled (str, optional): Whether to use RAG or skip it.
    Returns:
        dict: The extracted fields.
    """
    logging.info(f"extraction_type: {extraction_type}")
    logging.info(f"ocr_type: {ocr_type}")

    data_points = await validate_user_questions(queries, data_points)

    pages_list = await get_list_of_pages(page_range)
    logging.info(f"Pages to use: {pages_list}")

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request, pages_list
    )
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    logging.info("Working on extracting fields.")

    response = await extract_data_points_with_context(
        request,
        extraction_type,
        data_points,
        SourceDocument(document_json=document_json),
        llm_platform,
        embedding_platform,
        rag_enabled=rag_enabled,
        llm_reasoning_enabled=llm_reasoning_enabled,
    )

    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )

    return response
