import logging
from langchain_community.callbacks import get_openai_callback
from langchain_core.prompts import PromptTemplate
from langchain.chains.summarize import load_summarize_chain
from langchain.chains.combine_documents import create_stuff_documents_chain
from langchain_core.output_parsers import Str<PERSON>utputParser
from utils.enums import Summary<PERSON>ethod, SummarizationPrompts, VOBSummarizationPrompt
from langchain.prompts.chat import (
    ChatPromptTemplate,
    SystemMessagePromptTemplate,
    HumanMessagePromptTemplate,
)
from utils import RecursiveWordTextSplitter, convert_text_to_dict
from concurrent.futures import ThreadPoolExecutor, as_completed
from langchain.docstore.document import Document
from constants import (
    LLM_WORKERS,
    SUMMARY_DOCUMENT_CHUNK_WORD_COUNT,
    SUMMARY_CHUNK_OVERLAPPING_WORD_COUNT,
    REDUCE_STEP_CHUNK_WORD_COUNT,
    MD_TO_HTML_SYSTEM_PROMPT,
    MD_TO_HTML_PROMPT,
)
from models.endpoint_summary import update_endpoint_summary
import markdown2
import re
from utils.cloud_utils import get_cloud_object, get_llm_deployment_name
from fastapi import Request
from typing import Union
from services.cloud_services.aws import AWSHelper
from services.cloud_services.azure import AzureHelper
from langchain_core.runnables import Runnable, RunnableSerializable
from typing import Any
from utils.document_utils import SourceDocument


class DocumentSplitter:
    """
    Class to split the document into chunks.
    """

    def __init__(self, chunk_size: int, chunk_overlap: int):
        """
        Initializes the DocumentSplitter class.
        Args:
            chunk_size (int): The size of the chunk.
            chunk_overlap (int): The overlap of the chunk.
        """
        self.splitter = RecursiveWordTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
        )

    def split(self, document_text: str) -> list[Document]:
        """
        Split the document into chunks.
        Args:
            document_text (str): The document text.
        Returns:
            list[Document]: The chunks of the document.
        """
        return self.splitter.create_documents(document_text)

    @staticmethod
    def split_by_word_count(
        documents: list[Document], max_words: int
    ) -> list[list[Document]]:
        """
        Split the document into chunks.
        Args:
            documents (list[Document]): The documents to split.
            max_words (int): The maximum number of words per chunk.
        Returns:
            list[list[Document]]: The chunks of the document.
        """
        chunks = []
        current_chunk = []
        current_chunk_word_count = 0
        for doc in documents:
            doc_word_count = len(doc.page_content.split())
            if current_chunk_word_count + doc_word_count <= max_words:
                current_chunk.append(doc)
                current_chunk_word_count += doc_word_count
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = [doc]
                current_chunk_word_count = doc_word_count
        if current_chunk:
            chunks.append(current_chunk)
        return chunks


class SummaryHelper:
    """
    Class to help with the summarization process.
    """

    @staticmethod
    def get_user_summary_prompts(
        summary_prompts: SummarizationPrompts,
    ) -> tuple[str, str, str, str]:
        """
        Get the user summary prompts.
        Args:
            summary_prompts (SummarizationPrompts): The summary prompts.
        Returns:
            tuple[str, str, str, str]: The summary prompt, refine prompt, map prompt, reduce prompt.
        """
        if not summary_prompts:
            prompt_details = SummarizationPrompts()
            summary_prompt, refine_prompt = prompt_details.get_refine_summary_prompts()
            map_prompt, reduce_prompt = prompt_details.get_map_reduce_summary_prompts()
        else:
            summary_prompt = summary_prompts.summary_prompt
            refine_prompt = summary_prompts.refine_prompt
            map_prompt = summary_prompts.map_prompt
            reduce_prompt = summary_prompts.reduce_prompt
        return summary_prompt, refine_prompt, map_prompt, reduce_prompt

    @staticmethod
    def get_user_vob_summary_prompt(
        summary_prompts: VOBSummarizationPrompt,
    ) -> str:
        """
        Get the user VOB summary prompt.
        Args:
            summary_prompts (VOBSummarizationPrompt): The summary prompt.
        Returns:
            str: The vob summary prompt.
        """
        if not summary_prompts:
            return VOBSummarizationPrompt().get_vob_summary_prompt()
        else:
            return summary_prompts.vob_summary_prompt

    @staticmethod
    def get_raw_document_text(source_document: SourceDocument) -> str:
        """
        Get the raw document text.
        Args:
            source_document: (SourceDocument): The document text or json.
        Returns:
            str: The raw document text.
        """
        if source_document.get("document_json"):
            return "\n\n".join(  # Page Separator
                doc.get("content", "")
                for doc in source_document["document_json"].values()
            )
        return source_document["document_text"]

    @staticmethod
    def convert_markdown_to_html(
        md_summary: str,
        token_track: dict,
        request: Request,
        selected_cloud_llm_object: Union[AWSHelper, AzureHelper],
        llm_reasoning_enabled: str = "false",
    ) -> str:
        """
        Convert the markdown summary to HTML.
        Args:
            md_summary (str): The markdown summary.
            token_track (dict): The token usage.
            request (Request): The request object.
            selected_cloud_llm_object (Union[AWSHelper, AzureHelper]): The selected cloud LLM object.
            llm_reasoning_enabled (str): The enablement of LLM reasoning.
        Returns:
            str: The HTML summary.
        """
        logging.info("Converting MD to HTML using LLM.")
        md_json = {"markdown_format": {md_summary}}
        json_to_fill = {"html_format": ""}
        query_prompt = MD_TO_HTML_PROMPT.format(
            md_json=md_json, json_to_fill=json_to_fill
        )
        response = selected_cloud_llm_object.get_llm_response_with_metadata(
            query_prompt,
            "json_object",
            MD_TO_HTML_SYSTEM_PROMPT,
            llm_reasoning_enabled,
        )
        token_track["total_tokens"] += response["total_tokens"]
        token_track["prompt_tokens"] += response["prompt_tokens"]
        token_track["completion_tokens"] += response["completion_tokens"]
        response = response.get("content", "")
        update_endpoint_summary(
            request=request,
            total_word_count=token_track["total_word_count"],
            total_tokens=token_track["total_tokens"],
            prompt_tokens=token_track["prompt_tokens"],
            completion_tokens=token_track["completion_tokens"],
            total_cost=token_track["total_cost"],
        )
        try:
            response_html = eval(response)
            return response_html["html_format"].replace("\n", "")
        except Exception as e:
            logging.exception(
                f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
            )
            return SummaryHelper.json_parsing_with_regex(md_summary, response)

    @staticmethod
    def json_parsing_with_regex(md_summary: str, html_raw_summary: str) -> str:
        """
        Parse the HTML summary using regex.
        Args:
            md_summary (str): The markdown summary.
            html_raw_summary (str): The HTML summary.
        Returns:
            str: The parsed HTML summary.
        """
        line_re = re.compile(r"\{[^\}]+\}")
        records = line_re.findall(html_raw_summary)
        try:
            md_summary_dict = convert_text_to_dict(records[0])
            return md_summary_dict["html_format"].replace("\n", "")
        except Exception as e:
            logging.exception(
                f"Not able to extract JSON with regex! Reason: {e}. Using markdown2 now."
            )
            return markdown2.markdown(md_summary).replace("\n", "")


class Summarization:
    """
    Class to handle the summarization process.
    """

    def __init__(
        self,
        request: Request,
        source_document: SourceDocument,
        system_template: str,
        summary_prompts: SummarizationPrompts,
        llm_platform: str,
        summary_method: SummaryMethod = SummaryMethod.MapReduce,
        llm_reasoning_enabled: str = "false",
    ):
        """
        Initializes the Summarization class.
        Args:
            request (Request): The request object.
            source_document: (SourceDocument): The document text or json.
            system_template (str): The system template.
            summary_prompts (PromptDetails): The summary prompts.
            llm_platform (str): The LLM platform.
            summary_method (SummaryMethod): The summary method.
            llm_reasoning_enabled (str): The enablement of LLM reasoning.
        """
        self.request = request
        self.system_template = system_template
        self.summary_prompts = summary_prompts
        self.summary_method = summary_method
        self.llm_reasoning_enabled = llm_reasoning_enabled
        self.selected_cloud_llm_object = get_cloud_object(llm_platform)
        self.llm = self.selected_cloud_llm_object.get_chat_model(llm_reasoning_enabled)
        self.document_text = SummaryHelper.get_raw_document_text(source_document)
        self.splitter = DocumentSplitter(
            SUMMARY_DOCUMENT_CHUNK_WORD_COUNT, SUMMARY_CHUNK_OVERLAPPING_WORD_COUNT
        )
        self.texts = self.splitter.split(self.document_text)
        self.request.state.token_tracker.set_word_count(
            len(self.document_text.split(" "))
        )
        self.request.state.endpoint_summary.set_llm_platform(
            self.selected_cloud_llm_object.cloud_name
        )
        request.state.endpoint_summary.set_llm_model(
            get_llm_deployment_name(llm_platform, llm_reasoning_enabled)
        )

    def summarize(self) -> str:
        """
        Summarize the document.
        Returns:
            str: The summary of the document.
        """
        logging.info(f"Splitted Document Chunks: {len(self.texts)}")
        if self.summary_method == SummaryMethod.Stuff:
            return self.generate_stuff_summary(
                SummaryHelper.get_user_vob_summary_prompt(self.summary_prompts)
            )
        elif self.summary_method == SummaryMethod.MapReduce:
            map_prompt, reduce_prompt = SummaryHelper.get_user_summary_prompts(
                self.summary_prompts
            )[2:]
            return self.generate_map_reduce_summary(map_prompt, reduce_prompt)
        elif self.summary_method == SummaryMethod.Refine:
            summary_prompt, refine_prompt = SummaryHelper.get_user_summary_prompts(
                self.summary_prompts
            )[:2]
            return self.generate_refine_summary(summary_prompt, refine_prompt)

    def generate_stuff_summary(self, vob_summary_prompt: str) -> str:
        """
        Generate the VOB summary with stuff method (1-shot).
        Args:
            vob_summary_prompt (str): The vob summary prompt template.
        Returns:
            str: The summary generated with stuff chain.
        """
        if self.llm_reasoning_enabled == "true" and isinstance(
            self.selected_cloud_llm_object, AzureHelper
        ):
            messages = [
                HumanMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(vob_summary_prompt),
            ]
        else:
            messages = [
                SystemMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(vob_summary_prompt),
            ]

        prompt = ChatPromptTemplate.from_messages(messages)
        stuff_chain = prompt | self.llm | StrOutputParser()

        vob_summary, token_usage = self.get_llm_summary_with_usage(
            stuff_chain, {"context": Document(page_content=self.document_text)}
        )

        self.request.state.token_tracker.update(token_usage)
        self._update_endpoint_summary()

        return SummaryHelper.convert_markdown_to_html(
            vob_summary,
            self.request.state.token_tracker.get_usage(),
            self.request,
            self.selected_cloud_llm_object,
            self.llm_reasoning_enabled,
        )

    def generate_refine_summary(
        self, summary_template: str, refine_template: str
    ) -> str:
        """
        Generate the refine summary.
        Args:
            summary_template (str): The summary template.
            refine_template (str): The refine template.
        Returns:
            str: The summary generated with refine chain.
        """
        if self.llm_reasoning_enabled == "true" and isinstance(
            self.selected_cloud_llm_object, AzureHelper
        ):
            messages = [
                HumanMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(summary_template),
            ]
        else:
            messages = [
                SystemMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(summary_template),
            ]

        prompt = ChatPromptTemplate.from_messages(messages)
        refine_prompt = PromptTemplate.from_template(refine_template)
        refine_chain = load_summarize_chain(
            llm=self.llm,
            chain_type="refine",
            question_prompt=prompt,
            refine_prompt=refine_prompt,
            return_intermediate_steps=True,
            document_variable_name="docs",
            initial_response_name="existing_summary",
            input_key="input_documents",
            output_key="output_text",
        )
        with get_openai_callback() as callback:
            result = refine_chain.invoke({"input_documents": self.texts})
            tokens_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }
            self.request.state.token_tracker.update(tokens_usage)
            self._update_endpoint_summary()
        return result

    def generate_map_reduce_summary(
        self, map_template: str, reduce_template: str
    ) -> str:
        """
        Generate the map reduce summary.
        Args:
            map_template (str): The map template.
            reduce_template (str): The reduce template.
        Returns:
            str: The summary generated with map reduce chain.
        """
        if self.llm_reasoning_enabled == "true" and isinstance(
            self.selected_cloud_llm_object, AzureHelper
        ):
            map_messages = [
                HumanMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(map_template),
            ]
        else:
            map_messages = [
                SystemMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(map_template),
            ]

        map_prompt = ChatPromptTemplate.from_messages(map_messages)
        map_chain = map_prompt | self.llm | StrOutputParser()
        combine_documents_chain = self.get_stuff_document_chain(reduce_template)
        map_results = self.map_phase(map_chain)
        final_summary = self.reduce_phase(map_results, combine_documents_chain)
        return SummaryHelper.convert_markdown_to_html(
            final_summary,
            self.request.state.token_tracker.get_usage(),
            self.request,
            self.selected_cloud_llm_object,
            self.llm_reasoning_enabled,
        )

    def map_phase(
        self, map_chain: RunnableSerializable[dict[str, Any], Any]
    ) -> list[Document]:
        """
        Map phase of the map reduce chain.
        Args:
            map_chain (RunnableSerializable[dict[str, Any], Any]): The map chain.
        Returns:
            list[Document]: The results of the map phase.
        """
        map_results = []

        def map_step(doc: Document) -> tuple[str, dict]:
            """
            Map step of the map reduce chain.
            Args:
                doc (Document): The document to map.
            Returns:
                tuple[str, dict]: The result of the map step and the token usage.
            """
            result, token_usage = self.get_llm_summary_with_usage(
                map_chain, {"docs": doc}
            )
            return result, token_usage

        with ThreadPoolExecutor(max_workers=LLM_WORKERS) as executor:
            future_to_doc = {executor.submit(map_step, doc): doc for doc in self.texts}
            for future in as_completed(future_to_doc):
                result, token_usage = future.result()
                map_results.append(result)
                self.request.state.token_tracker.update(token_usage)
                self._update_endpoint_summary()

        map_results_final = [Document(page_content=res) for res in map_results]
        logging.info(
            f"Summarized using Map Chain, summarized documents: {len(map_results_final)}"
        )
        return map_results_final

    def reduce_phase(
        self,
        map_results_final: list[Document],
        combine_documents_chain: RunnableSerializable[dict[str, Any], Any],
    ) -> str:
        """
        Reduce phase of the map reduce chain.
        Args:
            map_results_final (list[Document]): The results of the map phase.
            combine_documents_chain (RunnableSerializable[dict[str, Any], Any]): The combine documents chain.
        Returns:
            str: The summary generated with map reduce chain.
        """
        chunks = DocumentSplitter.split_by_word_count(
            map_results_final, REDUCE_STEP_CHUNK_WORD_COUNT
        )
        logging.info(f"Total Chunks for Reduce: {len(chunks)}")

        def reduce_step(docs: list[Document]) -> tuple[str, dict]:
            """
            Reduce step of the map reduce chain.
            Args:
                docs (list[Document]): The documents to reduce.
            Returns:
                tuple[str, dict]: The result of the reduce step and the token usage.
            """
            result, token_usage = self.get_llm_summary_with_usage(
                combine_documents_chain, {"docs": docs}
            )
            return result, token_usage

        while len(chunks) > 1:
            reduced_results = []
            with ThreadPoolExecutor(max_workers=LLM_WORKERS) as executor:
                future_to_chunk = {
                    executor.submit(reduce_step, chunk): chunk for chunk in chunks
                }
                for future in as_completed(future_to_chunk):
                    result, token_usage = future.result()
                    reduced_results.append(result)
                    self.request.state.token_tracker.update(token_usage)
                    self._update_endpoint_summary()

            final_reduce_results = [
                Document(page_content=res) for res in reduced_results
            ]
            chunks = DocumentSplitter.split_by_word_count(
                final_reduce_results, REDUCE_STEP_CHUNK_WORD_COUNT
            )
            logging.info(f"Chunks for Next Reduce: {len(chunks)}")

        logging.info(f"Final Summary Call with 1 Chunk of Documents: {len(chunks[0])}")
        final_summary, token_usage = self.get_llm_summary_with_usage(
            combine_documents_chain, {"docs": chunks[0]}
        )
        self.request.state.token_tracker.update(token_usage)
        self._update_endpoint_summary()
        return final_summary

    def get_stuff_document_chain(
        self, reduce_template: str
    ) -> Runnable[dict[str, Any], Any]:
        """
        Get the stuff document chain.
        Args:
            reduce_template (str): The reduce template.
        Returns:
            Runnable[dict[str, Any], Any]: The stuff document chain.
        """
        if self.llm_reasoning_enabled == "true" and isinstance(
            self.selected_cloud_llm_object, AzureHelper
        ):
            reduce_messages = [
                HumanMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(reduce_template),
            ]
        else:
            reduce_messages = [
                SystemMessagePromptTemplate.from_template(self.system_template),
                HumanMessagePromptTemplate.from_template(reduce_template),
            ]
        reduce_prompt = ChatPromptTemplate.from_messages(reduce_messages)
        return create_stuff_documents_chain(
            self.llm,
            reduce_prompt,
            document_variable_name="docs",
            output_parser=StrOutputParser(),
        )

    def get_llm_summary_with_usage(
        self,
        chain: RunnableSerializable[dict, str],
        input_docs: Union[list[Document], Document],
    ) -> tuple[str, dict[str, int]]:
        """
        Get the LLM summary with usage.
        Args:
            chain (RunnableSerializable[dict, str]): The chain to track.
            input_docs (Union[list[Document], Document]): The input documents.
        Returns:
            tuple[str, dict[str, int]]: The result of the chain and the token usage.
        """
        with get_openai_callback() as callback:
            result = chain.invoke(input_docs)
            token_usage = {
                "total_tokens": callback.total_tokens,
                "prompt_tokens": callback.prompt_tokens,
                "completion_tokens": callback.completion_tokens,
                "total_cost": callback.total_cost,
            }
        return result, token_usage

    def _update_endpoint_summary(self) -> None:
        """
        Updates the endpoint summary.
        """
        usage = self.request.state.token_tracker.get_usage()
        update_endpoint_summary(
            self.request,
            total_word_count=usage["total_word_count"],
            total_tokens=usage["total_tokens"],
            prompt_tokens=usage["prompt_tokens"],
            completion_tokens=usage["completion_tokens"],
            total_embedding_tokens=usage["total_embedding_tokens"],
            total_cost=usage["total_cost"],
        )
