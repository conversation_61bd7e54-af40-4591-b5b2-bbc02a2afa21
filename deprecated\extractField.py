from constants import PDFRawTexts
import os
import json


def savePDFText(pages_text, store_name):
    """Save the PDF text in JSON Format.

    Args:
        pages_text (JSON): JSON containing the OCR data.
        store_name (string): Name of the PDF for saving.

    Returns:
        None
    """

    file_created = False

    with open(PDFRawTexts + f"//{store_name}.json", "w") as f:
        try:
            json.dump(pages_text, f, ensure_ascii=False)
            file_created = True
            f.close()
        except:
            f.close()

    if file_created == False:
        os.remove(PDFRawTexts + f"//{store_name}.json")
