from flask import Flask, request, Response
import os
import json
from constants import PDFRawTexts, HTMLRawTexts, Documents, PopplerPath
from frequency import getKeywordCount
from extractField import getFields
from textExtraction import load_pdf
from parseField import getHTMLText
from AzureFunctions import loadEmbeddings
import traceback
import time
from testingExtractField import runTest
import logging
from pdf2image import pdfinfo_from_path

logging.basicConfig(
    format='%(asctime)s [%(funcName)s] %(levelname)s: %(message)s',
    level=logging.INFO,
    datefmt='%Y-%m-%d %H:%M:%S'
)

app = Flask(__name__)

if os.path.exists(PDFRawTexts) == False:
    os.mkdir(PDFRawTexts)
    
if os.path.exists(HTMLRawTexts) == False:
    os.mkdir(HTMLRawTexts)

try:
    path = os.path.dirname(os.path.abspath(__file__))
    upload_folder=os.path.join(Documents)
    os.makedirs(upload_folder, exist_ok=True)
    app.config['upload_folder'] = upload_folder
except Exception as e:
    pass

embedding_model = loadEmbeddings()

@app.route('/')
def index():
    return Response(json.dumps({'Status': True, 'message': 'PDFPlus API is working..'}), mimetype='application/json')

@app.route('/testing', methods=['GET'])
def testing():
    return runTest()

@app.route('/frequency', methods=['POST'])
def frequency():

    try:
        query = request.form['keywords']

        if len(query) < 1:
            return {"Warning":"Parameter 'queries' cannot be empty."}, 400
        
        logging.info("query:"+query)

    except:
        return {"Warning":"Please pass the 'queries' paramter to this API."}, 400

    try:
        pdf_file = request.files['pdf']

        if pdf_file.filename == '':
            return {"Warning":"Parameter 'pdf' cannot be empty, please upload the .pdf file."}, 400

        if not pdf_file.filename.lower().endswith(".pdf"):
            return {"Warning":"The API expect only .pdf files."}, 400

    except:
        return {"Warning":"Please pass the 'pdf' paramter to this API."}, 400

    try:
        ocr_type = request.form['ocr_type']
    except:
        ocr_type = 'paddle'

    logging.info("ocr_type: "+ocr_type)

    try:
        pdf_name = pdf_file.filename
        save_path = os.path.join(app.config.get('upload_folder'), pdf_name)
        pdf_file.save(save_path)
        
        logging.info("Extracting PDF Text.")
        start = time.time()
        pdf_text = load_pdf(pdf_name, 0, 0,  api="freqs", ocr_type=ocr_type, demographic=False)
        ocr_time = time.time()-start
        logging.info("PDF Text Extracted.")

        logging.info("Calculating Frequencies.")
        res = getKeywordCount(pdf_text, query)

        res["ocr_text"] = pdf_text
        res["ocr_time"] = ocr_time
        
        return res
    
    except Exception as e:
        return str(e)

@app.route('/parseFields', methods=['POST'])
def extractHTMLFields():

    try:
        query = request.form['queries']

        if len(query) < 1:
            return {"Warning":"Parameter 'queries' cannot be empty."}, 400

    except Exception as e:
        return {"Warning":"Please pass the 'queries' paramter to this API."}, 400

    try:
        file = request.files['html']

        if file.filename == '':
            return {"Warning":"Parameter 'file' cannot be empty, please upload the .html file."}, 400

        if not file.filename.lower().endswith(".html"):
            return {"Warning":"The API expect only .html files."}, 400

    except:
        return {"Warning":"Please pass the 'html' paramter to this API."}, 400

    try:
        model_type = request.form['model_type']
    except:
        model_type = 'af'

    try:

        logging.info("query: "+query)
        logging.info("model_type: "+model_type)
        
        file_name = file.filename
        save_path = os.path.join(
        app.config.get('upload_folder'),file_name)
        file.save(save_path)
        
        logging.info("Extracing Text..")
        
        html_content = getHTMLText(file_name)
        
        logging.info("Text Extracted..")
        
        return getFields(embedding_model, model_type, query, html_content, html=True)
    
    except Exception as e:    
        return str(e), 500

@app.route('/clean', methods=['GET'])
def cleanDirectory():

    try:
        _ = [os.remove(Documents+"//"+i) for i in os.listdir(Documents)]
        _ = [os.remove(PDFRawTexts+"//"+i) for i in os.listdir(PDFRawTexts) if "ipynb_checkpoints" not in i]
        _ = [os.remove(HTMLRawTexts+"//"+i) for i in os.listdir(HTMLRawTexts) if "ipynb_checkpoints" not in i]
        
        return "Data deleted from the server."
    except:
        return "There was some error deleting some files."

@app.route('/extractFields', methods=['POST'])
def extractFields():

    start_time = time.time()
    
    logging.info("Extract Field is called!")

    try:
        query = request.form['queries']

        if len(query) < 1:
            return {"Warning":"Parameter 'queries' cannot be empty."}, 400

        logging.info("query: "+query)

    except:
        return {"Warning":"Please pass the 'queries' paramter to this API."}, 400

    try:
        file = request.files['pdf']

        if file.filename == '':
            return {"Warning":"Parameter 'pdf' cannot be empty, please upload the .pdf file."}, 400

        if file.filename.endswith(".pdf") == False:
            return {"Warning":"The API expect only .pdf files."}, 400

    except Exception as e:
        return {"Warning":"Please pass the 'pdf' paramter to this API."}, 400

    try:
        pdf_name = file.filename
        save_path = os.path.join(app.config.get('upload_folder'),pdf_name)
        file.save(save_path)
    except:
        return {"Warning":"Error in saving the PDF."}, 400

    try:
        model_type = request.form['model_type']
    except:
        model_type = 'af'

    logging.info("model_type: "+model_type)

    try:
        ocr_type = request.form['ocr_type']
    except:
        ocr_type = 'paddle'

    logging.info("ocr_type: "+ocr_type)

    try:
        pages = request.form['pages']

        if "-" not in pages or len(pages.split("-")) > 2:
            return {"Warning":"Please make sure to pass 'pages' in the correct range format. i.e, (starting-ending)"}, 400

        pages = pages.split("-")

        starting = int(pages[0])-1
        ending = int(pages[1])

    except Exception as e:
        info = pdfinfo_from_path(Documents+"//"+pdf_name, poppler_path=PopplerPath)
        starting = 0
        ending = info["Pages"]

    try:
        if starting < 1:
            starting = 0

        if starting > ending:
            return {"Warning":"Starting page cannot be greater than ending page."}, 400
    
    except Exception as e:
        return {"Warning":"Make sure to pass integer page numbers."}, 400
    
    try:
        logging.info("Extracting PDF Text")
        start_ocr = time.time()
        output = load_pdf(pdf_name, starting, ending, api="fields", ocr_type=ocr_type, demographic=True)
        
        #return output

        total_ocr = time.time() - start_ocr
        logging.info("PDF Text Extracted")

        logging.info("Working on AI Part.")
        ai_reponse = getFields(embedding_model, model_type, query, output)
        
        return {"response": ai_reponse, "ocr_time":total_ocr, "total_time": time.time() - start_time}
    
    except Exception as e:
        logging.info(traceback.print_exc())
        return str(e), 400

if __name__ == "__main__":

    app.run(host='0.0.0.0', port=9000)