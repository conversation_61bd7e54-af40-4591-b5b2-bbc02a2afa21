from utils.enums import OCRType
from typing import Dict, Any
from db.db_operations import save_endpoint_summary
from fastapi import Request
from models.endpoint_summary import EndpointSummary
from constants import *

_LLM_MODELS_COST_PER_1K_TOKENS = {
    "anthropic.claude-3-haiku-20240307-v1:0": {
        "input_tokens_cost": CLAUDE_3_HAIKU_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": CLAUDE_3_HAIKU_COST_PER_1K_OUTPUT_TOKENS,
    },
    "anthropic.claude-3-5-haiku-20241022-v1:0": {
        "input_tokens_cost": CLAUDE_3_5_HAIKU_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": CLAUDE_3_5_HAIKU_COST_PER_1K_OUTPUT_TOKENS,
    },
    "anthropic.claude-3-5-sonnet-20240620-v1:0": {
        "input_tokens_cost": CLAUDE_3_5_SONNET_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": CLAUDE_3_5_SONNET_COST_PER_1K_OUTPUT_TOKENS,
    },
    "anthropic.claude-3-sonnet-20240229-v1:0": {
        "input_tokens_cost": CLAUDE_3_SONNET_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": CLAUDE_3_SONNET_COST_PER_1K_OUTPUT_TOKENS,
    },
    "gpt-4o-mini": {
        "input_tokens_cost": GPT_4O_MINI_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": GPT_4O_MINI_COST_PER_1K_OUTPUT_TOKENS,
    },
    "o1-mini": {
        "input_tokens_cost": GPT_O1_MINI_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": GPT_O1_MINI_COST_PER_1K_OUTPUT_TOKENS,
    },
    "o3-mini": {
        "input_tokens_cost": GPT_O3_MINI_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": GPT_O3_MINI_COST_PER_1K_OUTPUT_TOKENS,
    },
    "gpt-4.1-mini": {
        "input_tokens_cost": GPT_4_1_MINI_COST_PER_1K_INPUT_TOKENS,
        "output_tokens_cost": GPT_4_1_MINI_COST_PER_1K_OUTPUT_TOKENS,
    },
}

_EMBEDDING_MODELS_COST_PER_1K_TOKENS = {
    "amazon.titan-embed-text-v2:0": AMAZON_TITAN_EMBED_V2_COST_PER_1K_TOKENS,
    "text-embedding-3-large": TEXT_EMBEDDING_3_LARGE_COST_PER_1K_TOKENS,
    "text-embedding-3-small": TEXT_EMBEDDING_3_SMALL_COST_PER_1K_TOKENS,
}


async def calculate_ocr_cost(ocr_type: str, image_pages_count: int) -> float:
    """
    Calculate the cost of OCR based on the OCR type and number of pages.
    Args:
        ocr_type (OCRType): The type of OCR to be used.
        image_pages_count (int): The number of pages in the PDF file.
    Returns:
        float: The cost of OCR.
    """
    ocr_cost = 0.0
    if ocr_type == OCRType.AWS:
        ocr_cost = AWS_TEXTRACT_COST_PER_1K_PAGES * (image_pages_count / 1000)
    elif ocr_type == OCRType.Azure:
        ocr_cost = DOCUMENT_INTELLIGENCE_COST_PER_1K_PAGES * (image_pages_count / 1000)

    return round(ocr_cost, 5)


async def calculate_llm_and_embedding_cost(
    llm_model: str,
    embedding_model: str,
    llm_input_tokens: str,
    llm_output_models: str,
    embedding_tokens: str,
) -> Dict[str, Any]:
    """
    Calculate the cost of LLM and embedding based on the model and tokens.
    Args:
        llm_model (str): The LLM model to be used.
        embedding_model (str): The embedding model to be used.
        llm_input_tokens (str): The number of input tokens for LLM.
        llm_output_models (str): The number of output tokens for LLM.
        embedding_tokens (str): The number of tokens for embedding.
    Returns:
        Dict[str, Any]: The cost of LLM and embedding.
    """
    llm_cost = 0.0
    embedding_cost = 0.0

    if llm_model in _LLM_MODELS_COST_PER_1K_TOKENS:
        llm_cost = (
            _LLM_MODELS_COST_PER_1K_TOKENS[llm_model]["input_tokens_cost"]
            * int(llm_input_tokens)
            / 1000
        )
        llm_cost += (
            _LLM_MODELS_COST_PER_1K_TOKENS[llm_model]["output_tokens_cost"]
            * int(llm_output_models)
            / 1000
        )

    if embedding_model in _EMBEDDING_MODELS_COST_PER_1K_TOKENS:
        embedding_cost = (
            _EMBEDDING_MODELS_COST_PER_1K_TOKENS[embedding_model]
            * int(embedding_tokens)
            / 1000
        )

    return {
        "llmCost": round(llm_cost, 5),
        "embeddingCost": round(embedding_cost, 5),
    }


async def save_endpoint_summary_and_add_cost_in_the_response(
    request: Request, response: Dict[str, Any], ocr_cost: float = 0.0
) -> Dict[str, Any]:
    """
    Save the endpoint summary and add cost in the response.
    Args:
        request (Request): The request object.
        response (Dict[str, Any]): The response to be updated.
        ocr_cost (float, optional): The cost of OCR. Defaults to 0.0.
    Returns:
        Dict[str, Any]: The updated response.
    """
    endpoint_summary: EndpointSummary = request.state.endpoint_summary
    await save_endpoint_summary(request.state.db, endpoint_summary)
    llm_and_embedding_cost = await calculate_llm_and_embedding_cost(
        endpoint_summary.llm_model,
        endpoint_summary.embedding_model,
        endpoint_summary.prompt_tokens,
        endpoint_summary.completion_tokens,
        endpoint_summary.total_embedding_tokens,
    )
    response["usageCost"] = llm_and_embedding_cost
    response["usageCost"]["ocrCost"] = ocr_cost
    return response
