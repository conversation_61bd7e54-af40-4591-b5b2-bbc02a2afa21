from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_FREQUENCY_AND_SUMMARY,
    OCR_PLATFORM_FREQUENCY_AND_SUMMARY,
    DEFAULT_SUMMARY_SYSTEM_PROMPT,
    LLM_REASONING_ENABLED_FREQUENCY_AND_SUMMARY,
)
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from services.frequency_and_summary_service import get_frequency_and_summary
from utils.enums import (
    OCRType,
    CloudPlatform,
    SummaryMethod,
    SummarizationPrompts,
    EndpointDescriptions,
)
from utils.cloud_utils import (
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(
    OCR_PLATFORM_FREQUENCY_AND_SUMMARY, "/frequency-and-summary"
)
LLM_PLATFORM = get_endpoint_llm_platform(
    LLM_PLATFORM_FREQUENCY_AND_SUMMARY, "/frequency-and-summary"
)
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    LLM_REASONING_ENABLED_FREQUENCY_AND_SUMMARY, "/frequency-and-summary"
)

frequency_and_summary_router = APIRouter()


@frequency_and_summary_router.post(
    "/frequency-and-summary",
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.frequency_summary,
)
async def frequency_and_summary(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    keywords: str = Form(
        ..., description="Comma separated list of keywords for frequencies."
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    method: SummaryMethod = Form(
        SummaryMethod.MapReduce,
        description="Optional: Summary Method, could be 'map_reduce' or 'refine'. By default 'map_reduce'.",
    ),
    act_prompt: str = Form(
        DEFAULT_SUMMARY_SYSTEM_PROMPT,
        description="Optional: Acting Prompt for LLM, like 'Act as a Skilled Nursing healthcare expert....'.",
    ),
    summary_prompts: SummarizationPrompts = Form(
        None,
        description="Optional: Prompts for AI on how and what to focus while summarizing.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    Calculate the frequency and summary of the PDF document.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        keywords (str): The comma separated list of keywords.
        llm_platform (Platform, optional): The LLM platform to use.
        method (SummaryMethod, optional): The summarization method.
        act_prompt (str, optional): The acting prompt.
        summary_prompts (PromptDetails, optional): The prompts for AI on how and what to focus while summarizing.
        ocr_type (OCRType, optional): The OCR type.
    Returns:
        dict: The frequency and summary of the PDF document.
    """
    logging.info(f"summarization method: {method}")
    logging.info(f"ocr_type: {ocr_type}")

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )

    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    response = await get_frequency_and_summary(
        request,
        document_json,
        keywords,
        act_prompt,
        summary_prompts,
        method,
        llm_platform,
        llm_reasoning_enabled,
    )

    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )

    return response
