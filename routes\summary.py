from fastapi import (
    Depends,
    APIRouter,
    Request,
    UploadFile,
    Form,
    File,
)
import logging
from constants import (
    DISABLE_BASIC_AUTH,
    LLM_PLATFORM_SUMMARIZE,
    OCR_PLATFORM_SUMMARIZE,
    DEFAULT_SUMMARY_SYSTEM_PROMPT,
    LLM_REASONING_ENABLED_SUMMARIZE,
)
from file_processing.text_extraction import get_ocr_text_json
from auth import authenticate_request
from services.summarization_service import summarize
from utils.enums import (
    OCRType,
    CloudPlatform,
    SummaryMethod,
    SummarizationPrompts,
    EndpointDescriptions,
)
from utils.document_utils import SourceDocument
from utils.cloud_utils import (
    get_endpoint_ocr_platform,
    get_endpoint_llm_platform,
    get_endpoint_llm_reasoning_enabled,
)
from utils.cost_calculations_utils import (
    save_endpoint_summary_and_add_cost_in_the_response,
)

# Get the endpoint model configurations
OCR_PLATFORM = get_endpoint_ocr_platform(OCR_PLATFORM_SUMMARIZE, "/summarize")
LLM_PLATFORM = get_endpoint_llm_platform(LLM_PLATFORM_SUMMARIZE, "/summarize")
LLM_REASONING = get_endpoint_llm_reasoning_enabled(
    LLM_REASONING_ENABLED_SUMMARIZE, "/summarize"
)

summary_router = APIRouter()


@summary_router.post(
    "/summarize",
    dependencies=(
        [Depends(authenticate_request)] if DISABLE_BASIC_AUTH != "true" else None
    ),
    description=EndpointDescriptions.summary,
)
async def summarization(
    request: Request,
    pdf: UploadFile = File(
        None,
        description="PDF file to be processed. For pdf files with size less than 5MB but usage of pdf_url is recommended.",
    ),
    pdf_url: str = Form(
        None,
        description="PDF file url to be fetched and processed. For pdf files with size more than 5MB.",
    ),
    ocr_data: UploadFile = File(
        None,
        description="Optional: JSON file containing OCR text (Processed by /ocr endpoint). For JSON files with size less than 5MB but usage of ocr_data_url is recommended.",
    ),
    ocr_data_url: str = Form(
        None,
        description="Optional: OCR JSON file url to be fetched and processed. For ocr_data files with size more than 5MB.",
    ),
    llm_platform: CloudPlatform = Form(
        LLM_PLATFORM,
        description=f"Optional: LLM Platform to use, could be 'azure' or 'aws'. Default is {LLM_PLATFORM}.",
    ),
    method: SummaryMethod = Form(
        SummaryMethod.MapReduce,
        description="Optional: Summary Method, could be 'map_reduce' or 'refine'. By default 'map_reduce'.",
    ),
    act_prompt: str = Form(
        DEFAULT_SUMMARY_SYSTEM_PROMPT,
        description="Optional: Acting Prompt for LLM, like 'Act as a Skilled Nursing healthcare expert....'.",
    ),
    summary_prompts: SummarizationPrompts = Form(
        None,
        description="Optional: Prompts for AI on how and what to focus while summarizing.",
    ),
    ocr_type: OCRType = Form(
        OCR_PLATFORM,
        description=f"Optional: OCR Model, could be 'paddle', 'azure', or 'aws'. By default {OCR_PLATFORM}.",
    ),
    llm_reasoning_enabled: str = Form(
        LLM_REASONING,
        enum=["true", "false"],
        description=f"Optional: Enable LLM Reasoning to create chain-of-thoughts before answering, could be 'true' or 'false'. By default {LLM_REASONING}.",
    ),
) -> dict:
    """
    Summarize the PDF document.
    Args:
        request (Request): The request object.
        pdf (UploadFile, optional): The PDF file to be processed.
        pdf_url (str, optional): The URL of the PDF file.
        ocr_data (UploadFile, optional): The JSON file containing OCR text.
        ocr_data_url (str, optional): The URL of the JSON file.
        llm_platform (Platform, optional): The LLM platform to use.
        method (SummaryMethod, optional): The summarization method.
        act_prompt (str, optional): The acting prompt.
        summary_prompts (PromptDetails, optional): The prompts for AI on how and what to focus while summarizing.
        ocr_type (OCRType, optional): The OCR type.
    Returns:
        dict: The summary of the PDF document.
    """
    logging.info(f"summarization method: {method}")
    logging.info(f"ocr_type: {ocr_type}")

    document_json = await get_ocr_text_json(
        ocr_data, ocr_data_url, pdf_url, pdf, ocr_type, request
    )
    ocr_cost = document_json.pop("usageCost", {"ocrCost": 0})

    logging.info("Working on summarization part.")

    summary_text = summarize(
        request,
        SourceDocument(document_json=document_json),
        act_prompt,
        summary_prompts,
        llm_platform,
        method,
        llm_reasoning_enabled,
    )
    response = {"output_text": summary_text}
    response = await save_endpoint_summary_and_add_cost_in_the_response(
        request=request, response=response, ocr_cost=ocr_cost["ocrCost"]
    )
    return response
