class TokenTracker:
    """
    TokenTracker class for tracking token usage.
    """

    def __init__(self):
        """
        Initializes the TokenTracker class.
        """
        self.token_track = {
            "total_word_count": 0,
            "total_tokens": 0,
            "total_cost": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_embedding_tokens": 0,
        }

    def update(self, token_usage: dict) -> None:
        """
        Updates the token usage.
        Args:
            token_usage (dict): The token usage.
        """
        self.token_track["total_tokens"] += token_usage.get("total_tokens", 0)
        self.token_track["total_cost"] += token_usage.get("total_cost", 0)
        self.token_track["prompt_tokens"] += token_usage.get("prompt_tokens", 0)
        self.token_track["completion_tokens"] += token_usage.get("completion_tokens", 0)
        self.token_track["total_embedding_tokens"] += token_usage.get(
            "total_embedding_tokens", 0
        )

    def set_word_count(self, word_count: int) -> None:
        self.token_track["total_word_count"] = word_count

    def get_usage(self) -> dict:
        """
        Returns the token usage.
        Returns:
            dict: The token usage.
        """
        return self.token_track
