from utils.custom_text_splitter import RecursiveWordTextSplitter
from langchain_community.vectorstores import FAISS
from langchain.docstore.document import Document
import logging
from exceptions import ValidationError
from utils.cloud_utils import get_cloud_object
from constants import (
    EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
    EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
)
from fastapi import Request
from services.embeddings.custom_azure_openai_embeddings import (
    CustomAzureOpenAIEmbeddings,
)
from services.embeddings.custom_bedrock_embeddings import CustomBedrockEmbeddings
from typing import Union


def get_faiss_vector_store_with_usage_and_model(
    request: Request,
    embedding_platform: str,
    document_text: Union[str, dict],
    html: bool = False,
) -> tuple[FAISS, dict, Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings]]:
    """
    Creates a FAISS vector store with usage and model.
    Args:
        request (Request): The FastAPI request object.
        embedding_platform (str): The platform for embedding.
        document_text (Union[str, dict]): The document text.
        html (bool, optional): The HTML content (default is False).
    Returns:
        tuple[FAISS, dict, Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings]]: A tuple containing the FAISS vector store, usage information, and the embedding model class object.
    """
    selected_embedding_cloud_object = get_cloud_object(embedding_platform)
    embedding_model = selected_embedding_cloud_object.get_embedding_model()

    # Saving the embedding platform name
    request.state.endpoint_summary.set_embedding_platform(
        selected_embedding_cloud_object.cloud_name
    )

    text_splitter = RecursiveWordTextSplitter(
        chunk_size=EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
        chunk_overlap=EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
    )

    docs = []
    if not html and isinstance(document_text, dict):
        logging.info(f"Creating embeddings for {len(document_text)} pages.")
        for i in document_text:
            docs.append(
                Document(
                    page_content=document_text[i]["content"], metadata={"page": str(i)}
                )
            )
    else:
        docs.append(Document(page_content=document_text, metadata={"page": "HTML"}))

    texts = text_splitter.split_documents(docs)
    logging.info(f"{len(texts)} document chunks created.")

    if len(texts) < 1:
        raise ValidationError(
            "PDF file or OCR data is empty. Please pass file with some text."
        )

    logging.info("Creating embeddings.")

    embedded_docs = embedding_model.embed_documents(
        [text.page_content for text in texts]
    )
    usage = embedded_docs["usage"]
    db = create_embedding_db(texts, embedding_model, embedded_docs)
    return db, usage, embedding_model


def create_embedding_db(
    texts: list[Document],
    embedding_model: Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings],
    embedded_docs: list[list[float]],
) -> FAISS:
    """
    Creates a FAISS vector store with usage and model.
    Args:
        texts (list[Document]): The list of document texts.
        embedding_model (Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings]): The embedding model class object.
        embedded_docs (list[list[float]]): The embedded documents.
    Returns:
        FAISS: The FAISS vector store.
    """
    logging.info("Creating embedding DB.")
    db = FAISS.from_embeddings(
        text_embeddings=zip(
            [text.page_content for text in texts], embedded_docs["embeddings"]
        ),
        metadatas=[text.metadata for text in texts],
        embedding=embedding_model,
    )

    logging.info("Embedding DB created.")
    return db


def get_query_embeddings_with_metadata(
    embedding_model: Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings],
    query: str,
) -> tuple[list[float], dict, str]:
    """
    Gets the query embeddings with metadata.
    Args:
        embedding_model (Union[CustomAzureOpenAIEmbeddings, CustomBedrockEmbeddings]): The embedding model class object.
        query (str): The query.
    Returns:
        tuple[list[float], dict, str]: A tuple containing the query embeddings, usage information, and the embedding model.
    """
    response = embedding_model.embed_query(query)
    return response["embeddings"][0], response["usage"], response["model"]
