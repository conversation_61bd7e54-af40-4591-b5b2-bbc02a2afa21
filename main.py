import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from starlette.exceptions import HTTPException
from fastapi.exceptions import RequestValidationError
import logging
from constants import DISABLE_DATABASE_REPORTING, INSTANCE_TYPE
from mangum import Mangum
from db.base import Base
from db import engine
from handlers.exception_handler import (
    all_exception_handler,
    all_http_exception_handler,
    request_validation_exception_handler,
)
from middleware.middlewares import http_middleware
from routes import routers

app = FastAPI(
    title=f"PDFPlus API - {INSTANCE_TYPE} Instance",
    description="This API contains various endpoints to support Referral Decisioning Process.",
    swagger_ui_parameters={"syntaxHighlight": False, "displayRequestDuration": True},
)
handler = Mangum(app)

logging.basicConfig(
    format="%(asctime)s [%(funcName)s] %(levelname)s: %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)

app.add_exception_handler(Exception, all_exception_handler)
app.add_exception_handler(HTTPException, all_http_exception_handler)
app.add_exception_handler(RequestValidationError, request_validation_exception_handler)

if not DISABLE_DATABASE_REPORTING:
    Base.metadata.create_all(bind=engine)

app.middleware("http")(http_middleware)

for router in routers:
    app.include_router(router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
