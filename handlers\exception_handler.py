import logging
from fastapi import status, Request
from starlette.exceptions import HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from db.db_operations import save_endpoint_summary


async def all_exception_handler(request: Request, e: Exception) -> JSONResponse:
    """
    Exception handler for all exceptions.
    Args:
        request (Request): The request object.
        e (Exception): The exception object.
    Returns:
        JSONResponse: The JSON response.
    """
    await save_endpoint_summary(request.state.db, request.state.endpoint_summary)

    try:
        # This is throwing an error in Lambda
        logging.exception(f"Error caught in all_exception_handler")
    except:
        logging.error(str(e))

    return JSONResponse(
        content={"error": str(e)},
        status_code=(
            e.status_code
            if hasattr(e, "status_code")
            else status.HTTP_500_INTERNAL_SERVER_ERROR
        ),
    )


async def all_http_exception_handler(
    request: Request, e: HTTPException
) -> JSONResponse:
    """
    Exception handler for HTTP exceptions.
    Args:
        request (Request): The request object.
        e (HTTPException): The HTTP exception object.
    Returns:
        JSONResponse: The JSON response.
    """
    await save_endpoint_summary(request.state.db, request.state.endpoint_summary)

    logging.exception(f"Error caught in all_http_exception_handler")

    return JSONResponse(
        content={"error": e.detail},
        status_code=(
            e.status_code
            if hasattr(e, "status_code")
            else status.HTTP_500_INTERNAL_SERVER_ERROR
        ),
    )


async def request_validation_exception_handler(
    _, e: RequestValidationError
) -> JSONResponse:
    """
    Exception handler for request validation exceptions.
    Args:
        _: The request object.
        e (RequestValidationError): The request validation exception object.
    Returns:
        JSONResponse: The JSON response.
    """
    logging.exception(f"Error caught in request_validation_exception_handler")
    return JSONResponse(
        content={
            "error": f"The API expect {e.args[0][0]['loc'][1]}. {e.args[0][0]['msg']}"
        },
        status_code=status.HTTP_400_BAD_REQUEST,
    )
