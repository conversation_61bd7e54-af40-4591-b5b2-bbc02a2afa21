from typing import List, Dict, Any
import re

async def get_list_of_pages(page_range: str) -> List[int]:
    """
    Parses the page range string and returns a sorted list of unique page numbers.
    Args:
        page_range (str): The page range string (e.g., "1,3,5-8").
    Returns:
        List[int]: A sorted list of page numbers.
    """
    if not page_range:
        return None

    return sorted({
        p for start, end in re.findall(r'(\d+)(?:-(\d+))?', page_range)
        for p in range(int(start), int(end) + 1 if end else int(start) + 1)
    })

async def filter_json_by_pages(
    json_data: Dict[str, Any],
    pages_to_pick: List[int]
) -> Dict[str, Any]:
    """
    Filters the JSON object based on the given list of pages.
    Args:
        json_data (Dict[str, Any]): The OCR JSON data.
        pages_to_pick (List[int]): The list of page numbers to pick.
    Returns:
        Dict[str, Any]: The filtered OCR JSON data based on pages.
    """
    return {
        str(page): json_data[
            str(page)
        ] for page in pages_to_pick if str(page) in json_data
    }