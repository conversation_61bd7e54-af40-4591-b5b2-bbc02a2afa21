from db.base import Base
from sqlalchemy import Column, Integer, String, DateTime
from datetime import datetime, timezone
import time

class OcrSummary(Base):
    """
    The OCR summary model.
    This model is used to store the summary of the OCR.
    """
    __tablename__ = "ocr_summary"

    id = Column(Integer, primary_key=True, nullable=False, autoincrement=True)
    endpoint = Column(String, nullable=False)
    ocr_type = Column(String)
    total_pages = Column(Integer)
    image_pages = Column(Integer)
    text_pages = Column(Integer)
    received_at = Column(DateTime(timezone=True))
    time_taken = Column(Integer)
    client_name = Column(String)

    def set_summary(
        self,
        ocr_type : str,
        total_pages : int,
        image_pages : int,
        text_pages : int,
    ) -> None:
        """
        Set the summary for the OCR.
        Args:
            ocr_type (str): The type of OCR used.
            total_pages (int): The total number of pages in the document.
            image_pages (int): The number of pages with images.
            text_pages (int): The number of pages with text.
        """
        self.ocr_type = ocr_type
        self.total_pages = total_pages
        self.image_pages = image_pages
        self.text_pages = text_pages
        self.time_taken = time.time() - self.received_at
        self.received_at = (
            str(datetime.fromtimestamp(self.received_at).astimezone(timezone.utc)),
        )
