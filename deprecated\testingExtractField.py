from chromadb.config import Settings
import logging
import re
from constants import (
    folder_path,
    ground_truths,
    ocr_docs_azure,
    embedding_data,
    embedding_time,
    embedding_db,
)
import os
import json
import time
from AzureFunctions import loadEmbeddings, getAccuracy


CHROMA_SETTINGS = Settings(
    anonymized_telemetry=False,
    is_persistent=True,
)


def runTest():

    if os.path.exists(folder_path) == False:
        return {"Warning": "Testing folder doesn't exist."}, 500

    try:

        ground_truth_path = folder_path + "\\" + ground_truths + "\\testing.json"
        ground_truth = json.loads(open(ground_truth_path).read())

        data_points = json.loads(
            open(folder_path + "\\testing_extractFields.json").read()
        )

    except Exception as e:
        return {"Warning": "The ground truth files were not found. " + str(e)}, 500

    embedding_model = loadEmbeddings()

    ocr_times = {}
    embedding_times = {}

    for pdf_name in ground_truth:
        if os.path.exists(
            folder_path + "\\" + ocr_docs_azure + "\\" + pdf_name + ".json"
        ):
            with open(
                folder_path + "\\" + ocr_docs_azure + "\\" + pdf_name + ".json",
                encoding="utf8",
            ) as f:
                ocr_json = json.load(f)
                ocr_times[pdf_name] = ocr_json["ocr_time"]

    with open(
        folder_path
        + "\\"
        + embedding_data
        + "\\"
        + embedding_time
        + "\\embedding_times.json"
    ) as f:
        embd_times = json.load(f)

    for embd_key in embd_times:
        embedding_times[embd_key] = embd_times[embd_key]

    query = ",".join(list(data_points["query"].keys()))

    questions = ""

    for i in list(data_points["query"].keys()):
        questions += data_points["query"][i]["Question"] + ","

    # Remove last comma
    questions = questions[:-1]

    terms = ""

    for i in list(data_points["query"].keys()):
        terms += data_points["query"][i]["term"] + ","

    # Remove last comma
    terms = terms[:-1]

    """return {
        "question":questions,
        "terms":terms
    }"""

    dbs = list(ground_truth.keys())[:5]

    final_output = {}

    for db in dbs:

        db_path = folder_path + "\\" + embedding_data + "\\" + embedding_db + "\\" + db

        start = time.time()
        ai_response = getFieldsTestingImproved(
            query, terms, questions, embedding_model, db_path, model_choice="af"
        )
        # ai_response = getFieldsTesting(queries, embedding_model, db_path, model_choice='sf')
        end = time.time()

        if type(ai_response) == dict:

            ai_response_chunks = ai_response["Chunks"]
            ai_response_prompt = ai_response["Prompt"]

            del ai_response["Chunks"]
            del ai_response["Prompt"]

            """
            if name.replace(",","") in list(ai_response.keys()):
                ai_response["Patient Name"] = ai_response.pop(name.replace(",",""))
        
            if age.replace(",","") in list(ai_response.keys()):
                ai_response["Age"] = ai_response.pop(age.replace(",",""))
        
            if sex.replace(",","") in list(ai_response.keys()):
                ai_response["Sex"] = ai_response.pop(sex.replace(",",""))
        
            if dob.replace(",","") in list(ai_response.keys()):
                ai_response["DOB"] = ai_response.pop(dob.replace(",",""))

            if ssn.replace(",","") in list(ai_response.keys()):
                ai_response["SSN"] = ai_response.pop(ssn.replace(",",""))"""

            """if mrn.replace(",","") in list(ai_response.keys()):
                ai_response["MRN"] = ai_response.pop(mrn.replace(",",""))"""

            """if fin.replace(",","") in list(ai_response.keys()):
                ai_response["FIN"] = ai_response.pop(fin.replace(",",""))
        
            if insurance_number.replace(",","") in list(ai_response.keys()):
                ai_response["Insurance Policy Number"] = ai_response.pop(insurance_number.replace(",",""))
        
            if emergency_name.replace(",","") in list(ai_response.keys()):
                ai_response["Emergency Contact Name"] = ai_response.pop(emergency_name.replace(",",""))
        
            if guarantor_name.replace(",","") in list(ai_response.keys()):
                ai_response["Guarantor Name"] = ai_response.pop(guarantor_name.replace(",",""))

            if height.replace(",","") in list(ai_response.keys()):
                ai_response["Height"] = ai_response.pop(height.replace(",",""))

            if weight.replace(",","") in list(ai_response.keys()):
                ai_response["Weight"] = ai_response.pop(weight.replace(",",""))

            if allergies.replace(",","") in list(ai_response.keys()):
                ai_response["Allergies"] = ai_response.pop(allergies.replace(",",""))

            if diag_codes.replace(",","") in list(ai_response.keys()):
                ai_response["Diagnosis codes"] = ai_response.pop(diag_codes.replace(",",""))

            if medicare_no.replace(",","") in list(ai_response.keys()):
                ai_response["Medicare Number"] = ai_response.pop(medicare_no.replace(",",""))

            if medicaid_no.replace(",","") in list(ai_response.keys()):
                ai_response["Medicaid Number"] = ai_response.pop(medicaid_no.replace(",",""))

            if payer_name.replace(",","") in list(ai_response.keys()):
                ai_response["Payer Name"] = ai_response.pop(payer_name.replace(",",""))"""

            gpt_time = end - start

            g_t = ground_truth[db]
            correct_keys = 0
            total_keys = len(g_t)

            for k in g_t:
                if k in ai_response and ai_response[k] == g_t[k]:
                    correct_keys += 1

            accuracy = (correct_keys * 100) / total_keys

            try:
                gpt_accuracy = getAccuracy(ground_truth[db], ai_response)
                line_re = re.compile(r"\{[^\}]+\}")
                records = line_re.findall(gpt_accuracy)
                accuracy = eval(records[0])["accuracy"]
                logging.debug("Smart Accuracy.")
            except:
                pass

            final_output[db] = {
                "time": {
                    "ocr_time": ocr_times[db],
                    "embedding_time": embedding_times[db],
                    "gpt_time": gpt_time,
                    "total_time": ocr_times[db] + embedding_times[db] + gpt_time,
                },
                "accuracy": {"azure": accuracy},
                "response": {"actual": ground_truth[db], "pdfplus": ai_response},
                #'chunks':ai_response_chunks,
                #'Prompt':ai_response_prompt
            }

        else:

            gpt_time = end - start

            final_output[db] = {
                "time": {
                    "ocr_time": ocr_times[db],
                    "embedding_time": embedding_times[db],
                    "gpt_time": gpt_time,
                    "total_time": ocr_times[db] + embedding_times[db] + gpt_time,
                },
                "response": {"actual": ground_truth[db], "pdfplus": ai_response},
            }

    return final_output
