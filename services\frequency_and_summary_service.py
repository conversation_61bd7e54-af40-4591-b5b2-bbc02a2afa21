from concurrent.futures import ThreadPoolExecutor
import logging
from services.frequency_service import get_keyword_count
from services.summarization_service import summarize
from utils.document_utils import SourceDocument
from fastapi import Request
from utils.enums import SummaryMethod
from services.summarization_service import SummarizationPrompts


async def get_frequency_and_summary(
    request: Request,
    document_text_json: dict,
    keywords: list[str],
    system_template: str,
    summary_prompts: SummarizationPrompts,
    summary_method: SummaryMethod,
    llm_platform: str,
    llm_reasoning_enabled: str = "false",
) -> dict:
    """
    Get the frequency and summary of the document.
    Args:
        request (Request): The request object.
        document_text_json (dict): The document text JSON.
        keywords (list[str]): The keywords to count frequency.
        system_template (str): The system template.
        summary_prompts (PromptDetails): The summary prompts.
        summary_method (SummaryMethod): The summary method.
        llm_platform (str): The LLM platform.
        llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning. Default is "false".
    Returns:
        dict: The frequency and summary of the document.
    """
    with ThreadPoolExecutor(max_workers=2) as executor:
        # Submitting tasks to the executor
        logging.info("Calculating frequencies and generating summary.")
        future_freqs = executor.submit(get_keyword_count, document_text_json, keywords)
        future_summary = executor.submit(
            summarize,
            request,
            SourceDocument(document_json=document_text_json),
            system_template,
            summary_prompts,
            llm_platform,
            summary_method,
            llm_reasoning_enabled,
        )
        # Wait for results
        freqs = future_freqs.result()
        summary = future_summary.result()

    return {"frequencies": freqs, "summary": summary}
