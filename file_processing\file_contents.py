from fastapi import UploadFile, status
from typing import Optional
import httpx
from constants import X_API_KEY
from exceptions import (
    ValidationError,
    CustomError,
)
import logging


async def get_file_contents(
    file_type: str,
    file_url: Optional[str] = None,
    file: Optional[UploadFile] = None,
) -> bytes:
    """
    Retrieves the contents of a file (JSON, PDF, or HTML) from either a URL or an uploaded file.
    Args:
        file_type (str): The type of the file (JSON, PDF, HTML).
        file_url (str, optional): The URL of the file.
        file (UploadFile, optional): An uploaded file object.
    Raises:
        ValidationError: If there are issues with the provided URL or file.
        CustomError: For any other custom error cases.
        ValueError: If the provided file_type is not one of: ['pdf', 'html', 'json'].
    Returns:
        bytes: The raw bytes of the file, or None if neither URL nor file is provided.
    """
    if file_type.lower() not in ["pdf", "html", "json", "txt"]:
        raise ValueError(
            f"Invalid file_type: {file_type}. Must be one of: ['pdf', 'html', 'json', 'txt']."
        )

    if not (file or file_url):
        raise ValidationError(
            f"The API expects a {file_type} file or a {file_type}_url"
        )

    if file_url:
        logging.info(f"Document type: {file_type} | Document URL: {file_url}")
        async with httpx.AsyncClient() as client:
            headers = {"X-API-KEY": X_API_KEY}
            try:
                response = await client.get(file_url, headers=headers)
                response.raise_for_status()
                return response.content
            except httpx.HTTPStatusError as e:
                logging.exception(
                    f"Error fetching {file_type} from URL: {e.response.text}"
                )
                raise CustomError(
                    f"Error fetching {file_type} from URL: {e.response.text}",
                    e.response.status_code,
                )
            except Exception as e:
                logging.exception(f"Error fetching {file_type} from URL: {e}")
                raise CustomError(
                    f"Error fetching {file_type} from URL: {e}",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
    elif file:
        logging.info(f"Document attachment type: {file_type}")
        if not file.filename.lower().endswith(f".{file_type}"):
            raise ValidationError(
                f"Invalid file type. Only {file_type} files are accepted."
            )
        return await file.read()
