from fastapi import Request
import time
import logging
from constants import DISABLE_IP_WHITELIST, INSTANCE_TYPE, IP_WHITELIST
from exceptions import UnauthorizedError
from db import get_db
from models import OcrSummary, EndpointSummary
from typing import Callable
from fastapi import Response
from services.endpoint_handlers.tokens_tracker import TokenTracker


async def http_middleware(request: Request, call_next: Callable) -> Response:
    """
    The HTTP middleware function.
    Args:
        request (Request): The request object.
        call_next (Callable): The next middleware function.
    Returns:
        Response: The response object.
    """
    start_time = time.time()

    client_host = request.client.host
    url_path = request.url.path
    client_name = request.headers.get("X-LD-CLIENT", "InternalUser")
    data_id = request.headers.get("X-LD-DATA-ID", None)
    query_level = request.headers.get("X-LD-QUERY-LEVEL", None)

    logging.info(f"Received a request: {request.method} | {url_path} | Client IP: {client_host} | Client Name: {client_name} | Data ID: {data_id} | Query Level: {query_level}")

    request.state.db = next(get_db())

    request.state.ocr_summary = OcrSummary(
        **{
            "received_at": start_time,
            "endpoint": url_path,
            "client_name": client_name,
        }
    )

    request.state.endpoint_summary = EndpointSummary(
        **{
            "received_at": start_time,
            "endpoint": url_path,
            "word_count": 0,
            "total_tokens": 0,
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_cost": 0.0,
            "total_embedding_tokens": 0,
            "instance_type": INSTANCE_TYPE,
            "client_name": client_name,
        }
    )

    request.state.token_tracker = TokenTracker()

    if DISABLE_IP_WHITELIST == "false" and client_host not in IP_WHITELIST:
        logging.error(f"Unauthorized access for {client_host}!")
        raise UnauthorizedError("Unauthorized access for your IP")

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
