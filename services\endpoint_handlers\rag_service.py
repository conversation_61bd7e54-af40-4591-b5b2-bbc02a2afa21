import logging
import asyncio
from typing import Union, Optional, List, Tuple, Dict, Any
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from fastapi import Request, status
from utils import convert_text_to_dict
from utils.custom_text_splitter import RecursiveWordTextSplitter
from utils.cloud_utils import (
    get_cloud_object,
    get_embedding_deployment_name,
    get_llm_deployment_name,
)
from utils.document_utils import calculate_pdf_word_count
from constants import (
    EMBEDDING_WORKERS,
    RAG_ENABLED,
    RAG_QUERY_PROMPT,
    RAG_JSON_PROMPT,
    QA_SYSTEM_PROMPT,
    RAG_USER_QUESTION,
    QUERY_EXPANSION_SYSTEM_PROMPT,
    EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
    EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
    PARENT_CHUNK_WORD_COUNT,
    PARENT_CHUNK_OVERLAPPING_WORD_COUNT,
    NUMBER_OF_RETRIEVED_PARENT_CHUNKS,
    ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED,
    PARENT_CHUNK_ENABLED,
    NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT,
    NUMBER_OF_CHUNKS_FOR_LLM,
    DISABLE_DOCUMENT_RERANKING,
    RAG_WORKERS,
    RERANKING_AUTH_KEY,
    RERANKING_API_URL,
    DISABLE_DOCUMENT_RERANKING_AUTH,
    RERANKING_SERVICE_TIMEOUT,
    EXTRACT_FIELDS_SYSTEM_PROMPT,
    DATA_POINT_UNIT_SEPARATOR,
    DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT,
    VECTOR_STORE_WORKERS,
    DEFAULT_LLM_REASONING_ENABLED,
)
from models import update_endpoint_summary, DocumentClassification
from langchain_community.vectorstores import FAISS
from langchain.docstore.document import Document
import re
from exceptions import CustomError
from langchain_community.vectorstores.utils import DistanceStrategy
import faiss
from langchain_community.docstore.in_memory import InMemoryDocstore
from pydantic import BaseModel, Field
from utils.enums import ExtractionType
import json
import httpx
from utils.document_utils import SourceDocument
from utils.unit_conversion import convert_unit_of_measurement
from utils import post_process_data_points


class ParaphrasedQuery(BaseModel):
    """You have performed query expansion to generate a paraphrasing of a question."""

    paraphrased_query: str = Field(
        ...,
        description="The original question.",
    )


class Embedding:
    """
    Embedding class for embedding documents and queries.
    """

    def __init__(self, embedding_platform: str):
        """
        Initializes the Embedding class.
        Args:
            embedding_platform (str): The embedding platform.
        """
        self.embedding_object = get_cloud_object(embedding_platform)
        self.embedding_model = self.embedding_object.get_embedding_model()
        self.executor = ThreadPoolExecutor(max_workers=EMBEDDING_WORKERS)

    def embed_documents(self, texts: List[str]) -> Dict[str, Any]:
        """
        Embeds a list of texts.
        Args:
            texts (List[str]): The texts to embed.
        Returns:
            Dict[str, Any]: A dictionary containing the embeddings and usage.
        """
        return self.embedding_model.embed_documents(texts)

    async def embed_documents_async(self, texts: List[str]) -> Dict[str, Any]:
        """
        Embeds a list of texts.
        Args:
            texts (List[str]): The texts to embed.
        Returns:
            Dict[str, Any]: A dictionary containing the embeddings and usage.
        """
        loop = asyncio.get_running_loop()
        return loop.run_in_executor(
            self.executor,
            lambda: self.embedding_model.embed_documents(texts),
        )

    def embed_query(self, query: str) -> Tuple[List[float], Dict[str, Any], str]:
        """
        Embeds a query.
        Args:
            query (str): The query to embed.
        Returns:
            Tuple[List[float], Dict[str, Any], str]: A tuple containing the embeddings, usage, and model.
        """
        response = self.embedding_model.embed_query(query)
        return response["embeddings"][0], response["usage"], response["model"]

    async def embed_query_async(
        self, query: str
    ) -> Tuple[List[float], Dict[str, Any], str]:
        """
        Embeds a query.
        Args:
            query (str): The query to embed.
        Returns:
            Tuple[List[float], Dict[str, Any], str]: A tuple containing the embeddings, usage, and model.
        """
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(
            self.executor,
            lambda: self.embedding_model.embed_query(query),
        )
        return response["embeddings"][0], response["usage"], response["model"]


class FaissVectorStore:
    """
    FaissVectorStore class for creating and managing a FAISS vector store.
    """

    def __init__(
        self,
        request: Request,
        embedding_platform: str,
    ):
        """
        Initializes the FaissVectorStore class.
        Args:
            request (Request): The request object.
            embedding_platform (str): The embedding platform.
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        """
        self.request = request
        self.embedding = Embedding(embedding_platform)
        self.parent_chunks = None
        self.vector_store = None
        self.request.state.endpoint_summary.set_embedding_platform(
            self.embedding.embedding_object.cloud_name
        )
        self.request.state.endpoint_summary.set_embedding_model(
            get_embedding_deployment_name(embedding_platform)
        )
        self.executor = ThreadPoolExecutor(max_workers=VECTOR_STORE_WORKERS)

    def create_chunks(
        self, source_document: SourceDocument, html: bool = False
    ) -> List[Document]:
        """
        Creates chunks from the document text.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        chunks = self.convert_page_text_to_chunks(source_document, html)
        text_splitter = RecursiveWordTextSplitter(
            chunk_size=EMBEDDINGS_DOCUMENT_CHUNK_WORD_COUNT,
            chunk_overlap=EMBEDDINGS_CHUNK_OVERLAPPING_WORD_COUNT,
        )
        if PARENT_CHUNK_ENABLED == "true":
            self.parent_chunks = self.create_parent_chunks(chunks, html)
            texts = text_splitter.split_documents(self.parent_chunks, "child_chunk_num")
        else:
            texts = text_splitter.split_documents(chunks, "child_chunk_num")
        logging.info(f"{len(texts)} document chunks created.")
        return texts

    def convert_page_text_to_chunks(
        self, source_document: SourceDocument, html: bool = False
    ) -> List[Document]:
        """
        Converts the document text to chunk format.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        chunks = []
        if not html and source_document.get("document_json"):
            logging.info(
                f"Creating embeddings for {len(source_document.get('document_json'))} pages."
            )
            for idx, page_no in enumerate(source_document.get("document_json")):
                chunks.append(
                    Document(
                        page_content=source_document.get("document_json")[page_no].get(
                            "content", ""
                        ),
                        metadata={
                            "parent_chunk_num": str(idx + 1),
                            "page": str(page_no),
                        },
                    )
                )
        else:
            chunks.append(
                Document(
                    page_content=source_document.get("document_text"),
                    metadata={"parent_chunk_num": "1", "page": "HTML"},
                )
            )
        return chunks

    def create_parent_chunks(
        self, chunks: List[Document], html: bool = False
    ) -> List[Document]:
        """
        Create parent chunks.
        Args:
            chunks (List[Document]): The converted chunks from document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            List[Document]: A list of documents.
        """
        if not html and ENTIRE_PAGE_AS_PARENT_CHUNK_ENABLED == "true":
            return chunks
        parent_splitter = RecursiveWordTextSplitter(
            chunk_size=PARENT_CHUNK_WORD_COUNT,
            chunk_overlap=PARENT_CHUNK_OVERLAPPING_WORD_COUNT,
        )
        parent_chunks = parent_splitter.split_documents(chunks, "parent_chunk_num")
        return parent_chunks

    def get_top_k_parent_chunks(
        self, child_chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Returns parent chunks from top K child chunks and removes all child chunks of mapped parents.
        Args:
            child_chunks (List[Tuple[Document,float]]): The list of child chunks.
        Returns:
            List[Tuple[Document,float]]: The list of parent chunks.
        """
        parent_chunks_dict = {
            chunk.metadata["parent_chunk_num"]: chunk for chunk in self.parent_chunks
        }

        retrieved_parent_chunk_nums = set()
        combined_parent_child_chunks = []

        for i, child_chunk in enumerate(child_chunks):
            parent_chunk_num = child_chunk[0].metadata["parent_chunk_num"]
            if (
                i < NUMBER_OF_RETRIEVED_PARENT_CHUNKS
                and parent_chunk_num in parent_chunks_dict
                and parent_chunk_num not in retrieved_parent_chunk_nums
            ):
                combined_parent_child_chunks.append(
                    (parent_chunks_dict[parent_chunk_num], None)
                )
                retrieved_parent_chunk_nums.add(parent_chunk_num)
            elif parent_chunk_num not in retrieved_parent_chunk_nums:
                combined_parent_child_chunks.append(child_chunk)

        return combined_parent_child_chunks

    def create_document_embeddings(
        self, source_document: SourceDocument, html: bool = False
    ) -> Dict[str, Any]:
        """
        Creates document embeddings.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            Dict[str, Any]: A dict containing the embeddings of the document and the token usage.
        """
        data_with_embeddings = []
        texts = self.create_chunks(source_document, html)
        embedded_docs = self.embedding.embed_documents(
            [text.page_content for text in texts]
        )
        for idx, chunk_embeddings in enumerate(embedded_docs["embeddings"]):
            data_with_embeddings.append(
                {
                    "metadata": texts[idx].metadata,
                    "text": texts[idx].page_content,
                    "embedding": chunk_embeddings,
                }
            )
        return {"usage": embedded_docs["usage"], "embedding_data": data_with_embeddings}

    async def create_document_embeddings_async(
        self, source_document: SourceDocument, html: bool = False
    ) -> Dict[str, Any]:
        """
        Creates document embeddings.
        Args:
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            Dict[str, Any]: A dict containing the embeddings of the document and the token usage.
        """
        data_with_embeddings = []
        texts = self.create_chunks(source_document, html)
        embedded_docs = await self.embedding.embed_documents_async(
            [text.page_content for text in texts]
        )
        for idx, chunk_embeddings in enumerate(embedded_docs["embeddings"]):
            data_with_embeddings.append(
                {
                    "metadata": texts[idx].metadata,
                    "text": texts[idx].page_content,
                    "embedding": chunk_embeddings,
                }
            )
        return {"usage": embedded_docs["usage"], "embedding_data": data_with_embeddings}

    def initialize_vector_store_and_return_tokens(
        self, source_document: SourceDocument, html: bool = False
    ) -> Tuple[FAISS, int]:
        """
        Initialize a FAISS vector store.
        Args:
            source_document: (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
        Returns:
            Tuple[FAISS, int]: A tuple containing the FAISS vector store and the total number of embedding tokens.
        """
        embeddings_data = self.create_document_embeddings(source_document, html)
        total_embedding_tokens = embeddings_data["usage"]["total_tokens"]
        self.vector_store = FAISS(
            embedding_function=self.embedding.embedding_model,
            index=faiss.IndexFlatL2(
                len(embeddings_data["embedding_data"][0]["embedding"])
            ),
            distance_strategy=DistanceStrategy.EUCLIDEAN_DISTANCE,
            docstore=InMemoryDocstore(),
            index_to_docstore_id={},
        )
        _ = self.vector_store.add_embeddings(
            text_embeddings=zip(
                [
                    chunk_data["text"]
                    for chunk_data in embeddings_data["embedding_data"]
                ],
                [
                    chunk_data["embedding"]
                    for chunk_data in embeddings_data["embedding_data"]
                ],
            ),
            metadatas=[
                chunk_data["metadata"]
                for chunk_data in embeddings_data["embedding_data"]
            ],
        )

        return total_embedding_tokens

    async def similarity_search_async(self, query_embedding, k):
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            self.executor,
            lambda: self.vector_store.similarity_search_with_score_by_vector(
                query_embedding, k=k
            ),
        )

    def similarity_search(
        self, query_embedding: List[float], k: int = 2
    ) -> List[Tuple[Document, float]]:
        """
        Performs a similarity search and returns the relevant chunks.
        Args:
            query_embedding (List[float]): The query embedding.
            k (int, optional): The number of results to return. Default is 2.
        Returns:
            List[Tuple[Document, float]]: A list of tuples containing the documents and their scores.
        """
        if not self.vector_store:
            raise ValueError("Vector store has not been created yet.")
        return self.vector_store.similarity_search_with_score_by_vector(
            query_embedding, k=k
        )

    def embed_user_query(self, query: str) -> Tuple[List[float], Dict[str, Any], str]:
        """
        Gets the embeddings for the query.
        Args:
            query (str): The query.
        Returns:
            Tuple[List[float], Dict[str, Any], str]: A tuple containing the embeddings, token usage and the model used.
        """
        logging.info(f"Embedding the User Query: {query}")
        query_embedding, usage, _ = self.embedding.embed_query(query)
        return query_embedding, usage

    async def embed_user_query_async(
        self, query: str
    ) -> Tuple[List[float], Dict[str, Any], str]:
        """
        Gets the embeddings for the query.
        Args:
            query (str): The query.
        Returns:
            Tuple[List[float], Dict[str, Any], str]: A tuple containing the embeddings, token usage and the model used.
        """
        logging.info(f"Embedding the User Query: {query}")
        query_embedding, usage, _ = await self.embedding.embed_query_async(query)
        return query_embedding, usage


class RetrievalAugmentedGeneration:
    """
    RetrievalAugmentedGeneration class for performing retrieval-augmented generation.
    """

    def __init__(
        self,
        request: Request,
        llm_platform: str,
        embedding_platform: str,
        source_document: SourceDocument,
        html: bool = False,
        rag_enabled: str = RAG_ENABLED,
        llm_reasoning_enabled: str = DEFAULT_LLM_REASONING_ENABLED,
    ):
        """
        Initializes the RetrievalAugmentedGeneration class.
        Args:
            request (Request): The request object.
            llm_platform (str): The LLM platform.
            embedding_platform (str): The embedding platform.
            source_document (SourceDocument): The document text or json.
            html (bool, optional): Whether the document is HTML. Default is False.
            rag_enabled (str, optional): Whether to use RAG or skip it.
            llm_reasoning_enabled (str, optional): Whether to enable LLM reasoning. Default is LLM_REASONING_ENABLED.
        """
        self.request = request
        self.llm_object = get_cloud_object(llm_platform)
        self.llm_reasoning_enabled = llm_reasoning_enabled
        self.llm_deployment_name = get_llm_deployment_name(
            llm_platform, llm_reasoning_enabled
        )
        pdf_word_count = calculate_pdf_word_count(source_document)
        self.request.state.token_tracker.set_word_count(pdf_word_count)

        self.request.state.endpoint_summary.set_llm_platform(self.llm_object.cloud_name)

        if rag_enabled == "true":
            self.faiss_obj = FaissVectorStore(request, embedding_platform)
            total_embedding_tokens = (
                self.faiss_obj.initialize_vector_store_and_return_tokens(
                    source_document, html
                )
            )
            # Create vector store during initialization
            self.request.state.token_tracker.update(
                {"total_embedding_tokens": total_embedding_tokens}
            )
        else:
            self.document_text = source_document.get("document_text") or "\n".join(
                [
                    doc.get("content", "")
                    for doc in source_document.get("document_json").values()
                ]
            )

        self._update_endpoint_summary()
        self.executor = ThreadPoolExecutor(max_workers=RAG_WORKERS)

    async def get_context_for_llm(self, question: str) -> Tuple[str, Dict[str, Any]]:
        """
        Gets the context for the LLM.
        Args:
            question (str): The question.
        Returns:
            Tuple[str, Dict[str, Any]]: A tuple containing the context for the LLM and the relevant chunk pages.
        """
        # Get question variants through query expansion
        variants = await self.query_expansion_async(question)
        variants.append(question)  # Include original question

        # Get relevant chunks for each variant
        tasks = [
            self._get_relevant_docs_for_query_async(variant) for variant in variants
        ]
        all_chunks = await asyncio.gather(*tasks)

        # Remove duplicate chunks between variants
        relevant_chunks = self._remove_chunk_duplication(all_chunks)

        # Rerank chunks if reranking is not disabled
        if DISABLE_DOCUMENT_RERANKING == "false":
            logging.info(f"Reranking chunks for '{question}'")
            relevant_chunks = await self.document_reranking(question, relevant_chunks)
        else:
            # If reranking is disabled, sort chunks by score and pick top k chunks for LLM
            relevant_chunks = self._sort_chunks_by_score_and_pick_top_k(relevant_chunks)

        if PARENT_CHUNK_ENABLED == "true":
            relevant_chunks = self.faiss_obj.get_top_k_parent_chunks(
                relevant_chunks
            )  # Get parent chunks for top_K child chunks

        # Prepare chunks for LLM
        all_context, all_pages = self._prepare_chunks_for_llm(relevant_chunks)
        return all_context, all_pages

    async def _process_question(
        self,
        question: str,
        qa_prompt: str,
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Process a single question and get the answer with context.
        Args:
            question (str): The question to process.
            qa_prompt (str): The QA prompt.
        Returns:
            Tuple[str, Dict[str, Any]]: A tuple containing the question and its answer with context.
        """
        all_context, all_pages = await self.get_context_for_llm(question)
        question_prompt = (
            qa_prompt + "\n\n" + RAG_USER_QUESTION.format(question=question)
        )
        question_prompt = RAG_QUERY_PROMPT.format(
            context=all_context, question=question_prompt
        )
        response = self.llm_object.get_llm_response_with_metadata(
            question_prompt,
            "json_object",
            QA_SYSTEM_PROMPT,
            self.llm_reasoning_enabled,
        )
        self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
        self._update_token_track(response)
        self._update_endpoint_summary()

        llm_response = response.get("content", "")
        try:
            answer_json = convert_text_to_dict(llm_response)
        except Exception as e:
            logging.exception(
                f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
            )
            answer_json = self._extract_json_with_regex(llm_response)

        answer_json["response"] = ", ".join(answer_json.values())
        answer_json["pages"] = all_pages
        return question, answer_json

    async def get_answer_with_context(
        self,
        questions: List[str],
        qa_prompt: str,
        default_data_points: Optional[List[str]] = ["Height||in", "Weight||lbs"],
        rag_enabled: str = RAG_ENABLED,
    ) -> Dict[str, Any]:
        """
        Gets the answer with context.
        Args:
            questions (List[str]): The questions.
            qa_prompt (str): The QA prompt.
            default_data_points (str, optional): The default data points. Default is "Height (inches), Weight (pounds)".
        Returns:
            Dict[str, Any]: A dictionary containing the answers with context.
        """
        answers_with_context = {}
        valid_questions = [q.strip() for q in questions if q.strip()]

        if valid_questions:
            tasks = [
                self._process_question(question, qa_prompt)
                for question in valid_questions
            ]
            results = await asyncio.gather(*tasks)
            for question, answer in results:
                answers_with_context[question] = answer

        extract_data_points = self.extract_data_points(
            default_data_points, rag_enabled=rag_enabled
        )
        extract_data_points = await post_process_data_points(
            extract_data_points, default_data_points
        )
        answers_with_context["default"] = extract_data_points
        return answers_with_context

    async def _process_category_question(
        self,
        question: str,
        prompt: str,
        dynamic_doc_classification: Any,
        categories_description: str,
    ) -> List[Dict[str, Any]]:
        """
        Process a single category question.
        Args:
            question (str): The question to process.
            prompt (str): The prompt for document categorization.
            dynamic_doc_classification (Any): The document classification model.
            categories_description (str): The description of the categories.
        Returns:
            List[Dict[str, Any]]: A list of document category results.
        """
        try:
            all_context, all_pages = await self.get_context_for_llm(question)
            question_prompt = RAG_QUERY_PROMPT.format(
                context=all_context, question=prompt
            )

            llm_response_and_tokens_usage = await self.llm_object.get_llm_pydantic_schema_response_with_metadata_async(
                question_prompt,
                dynamic_doc_classification,
                DOCUMENT_CATEGORIZATION_SYSTEM_PROMPT.format(
                    DOCUMENT_CATEGORIES=categories_description
                ),
                self.llm_reasoning_enabled,
            )

            # Update token tracking and endpoint summary
            self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
            self._update_token_track(llm_response_and_tokens_usage["tokens_usage"])
            self._update_endpoint_summary()

            llm_response: List[DocumentClassification] = llm_response_and_tokens_usage[
                "llm_response"
            ]
            logging.info(
                f"Document Classification for question '{question}': {llm_response}"
            )

            if llm_response:
                return [
                    {
                        "documentCategory": doc_class.document_category,
                        "reasoning": doc_class.reasoning,
                        "confidenceScore": doc_class.confidence_score,
                        "pages": [int(page) for page in all_pages.keys()],
                    }
                    for doc_class in llm_response
                ]
        except Exception as e:
            logging.error(
                f"Exception while processing category question - {question}: {e}"
            )
        return []

    async def categorize_document(
        self, questions: List[str], prompt: str, categories_description: str
    ) -> Dict[str, Any]:
        """
        Categorize the document into categories based on the content.
        Args:
            questions (List[str]): The questions to categorize.
            prompt (str): The prompt for document categorization.
            categories_description (str): The description of the categories.
        Returns:
            Dict[str, Any]: A dictionary containing the categorization results.
        """
        logging.info("Using AI RAG to categorize the document.")
        answers_with_context = {"documentCategories": []}

        dynamic_doc_classification = DocumentClassification.with_categories_description(
            categories_description
        )

        tasks = [
            self._process_category_question(
                question, prompt, dynamic_doc_classification, categories_description
            )
            for question in questions
        ]
        results = await asyncio.gather(*tasks)
        for result in results:
            answers_with_context["documentCategories"].extend(result)

        return answers_with_context

    def _sort_chunks_by_score_and_pick_top_k(
        self, chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Sorts the chunks by score and picks the top k chunks.
        Args:
            chunks (List[Tuple[Document, float]]): The chunks to sort.
        Returns:
            List[Tuple[Document, float]]: The sorted list of the top k chunks.
        """
        chunks.sort(key=lambda x: x[1], reverse=False)
        return chunks[:NUMBER_OF_CHUNKS_FOR_LLM]

    def _remove_chunk_duplication(
        self, chunks_list: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Removes duplicate chunks based on chunk number in metadata.
        Args:
            chunks (List[Tuple[Document, float]]): The chunks to remove duplicates from.
        Returns:
            List[Tuple[Document, float]]: A list of unique chunks.
        """
        unique_chunks = {}
        for chunks in chunks_list:
            for chunk in chunks:
                chunk_num = chunk[0].metadata.get("child_chunk_num")
                if chunk_num not in unique_chunks:
                    chunk = (chunk[0], float(chunk[1]))
                    unique_chunks[chunk_num] = chunk
        return list(unique_chunks.values())

    def query_expansion(self, question: str) -> List[str]:
        """
        Expands a given question into multiple paraphrased variants using an LLM.
        Args:
            question (str): The original question to be expanded.
        Returns:
            List[str]: A list of paraphrased versions of the original question.
        """
        llm_response_and_tokens_usage = (
            self.llm_object.get_llm_pydantic_schema_response_with_metadata(
                question,
                ParaphrasedQuery,
                QUERY_EXPANSION_SYSTEM_PROMPT,
                self.llm_reasoning_enabled,
            )
        )
        self._update_token_track(llm_response_and_tokens_usage["tokens_usage"])
        self._update_endpoint_summary()
        llm_paraphrased_queries: List[ParaphrasedQuery] = llm_response_and_tokens_usage[
            "llm_response"
        ]
        logging.info(f"Query variants for '{question}': {llm_paraphrased_queries}")
        return [query.paraphrased_query for query in llm_paraphrased_queries]

    async def query_expansion_async(self, question: str) -> List[str]:
        """
        Expands a given question into multiple paraphrased variants using an LLM.
        Args:
            question (str): The original question to be expanded.
        Returns:
            List[str]: A list of paraphrased versions of the original question.
        """
        loop = asyncio.get_running_loop()
        llm_response_and_tokens_usage = await loop.run_in_executor(
            self.executor,
            lambda: self.llm_object.get_llm_pydantic_schema_response_with_metadata(
                question,
                ParaphrasedQuery,
                QUERY_EXPANSION_SYSTEM_PROMPT,
                self.llm_reasoning_enabled,
            ),
        )

        self._update_token_track(llm_response_and_tokens_usage["tokens_usage"])
        self._update_endpoint_summary()
        llm_paraphrased_queries: List[ParaphrasedQuery] = llm_response_and_tokens_usage[
            "llm_response"
        ]
        logging.info(f"Query variants for '{question}': {llm_paraphrased_queries}")
        return [query.paraphrased_query for query in llm_paraphrased_queries]

    async def document_reranking(
        self, query: str, relevant_chunks: List[Tuple[Document, float]]
    ) -> List[Tuple[Document, float]]:
        """
        Reranks the chunks based on the query.
        Args:
            query (str): The query.
            relevant_chunks (List[Tuple[Document, float]]): The chunks to rerank.
        Returns:
            List[Tuple[Document, float]]: A list of reranked chunks.
        """
        # Prepare chunks for reranking format
        chunks_json = [
            {
                "id": chunk[0].metadata.get("child_chunk_num"),
                "text": chunk[0].page_content,
                "metadata": chunk[0].metadata,
            }
            for chunk in relevant_chunks
        ]

        params = {"query": query, "K": NUMBER_OF_CHUNKS_FOR_LLM}
        files = {
            "chunks_file": (
                "chunks_file.json",
                json.dumps(chunks_json),
                "application/json",
            )
        }
        headers = None
        if DISABLE_DOCUMENT_RERANKING_AUTH != "true":
            headers = {"Authorization": f"Basic {RERANKING_AUTH_KEY}"}
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    RERANKING_API_URL,
                    params=params,
                    files=files,
                    headers=headers,
                    timeout=RERANKING_SERVICE_TIMEOUT,
                )
                response.raise_for_status()
                reranked_chunks = [
                    (
                        Document(
                            page_content=chunk["text"], metadata=chunk["metadata"]
                        ),
                        chunk["score"],
                    )
                    for chunk in response.json()["rerankedChunks"]
                ]
                return reranked_chunks
            except httpx.HTTPStatusError as e:
                logging.exception(f"Error occured while reranking: {e.response.text}")
                raise CustomError(
                    f"Error occured while reranking: {e.response.text}",
                    e.response.status_code,
                )
            except Exception as e:
                logging.exception(f"An error occurred while reranking: {e}")
                raise CustomError(
                    f"An error occurred while reranking: {e}",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

    def extract_data_points(
        self,
        data_points: List[str],
        extraction_method: str = "af",
        rag_enabled=RAG_ENABLED,
    ) -> Dict[str, Any]:
        """
        Extracts data points.
        Args:
            data_points (List[str]): The data points.
            extraction_method (str, optional): The extraction method. Default is "af".
            rag_enabled (str, optional): Whether to use RAG or skip it.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        if extraction_method == ExtractionType.AllField:
            return self._extract_all_fields(data_points, rag_enabled)
        else:
            return self._extract_separate_fields(data_points)

    def _extract_relevant_chunks(
        self, data_point: str
    ) -> Tuple[str, List, Dict[str, Any]]:
        """
        Extracts relevant chunks for a given data point.
        Args:
            data_point (str): The data point.
        Returns:
            Tuple[str, List, Dict[str, Any]]: A tuple containing the data point, context for the LLM, and relevant chunk pages.
        """
        data_point = data_point.strip()
        relevant_chunks = self._get_relevant_docs_for_query(
            data_point, k=NUMBER_OF_CHUNKS_FOR_LLM
        )
        context_for_llm, relevant_chunk_pages = self._get_prepared_chunks_for_llm(
            relevant_chunks
        )
        return data_point, context_for_llm, relevant_chunk_pages

    def _extract_all_fields(
        self, data_points: List[str], rag_enabled=RAG_ENABLED
    ) -> Dict[str, Any]:
        """
        Extracts all fields.
        Args:
            data_points (List[str]): The data points.
            rag_enabled (str, optional): Whether to use RAG or skip it.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        context_json = {}
        relevant_docs = set()

        json_to_fill = {
            data_point.split(DATA_POINT_UNIT_SEPARATOR)[0].strip(): ""
            for data_point in data_points
        }
        conversion_map = {
            data_point.split(DATA_POINT_UNIT_SEPARATOR)[0].strip(): {
                "data_point": data_point,
                "desired_unit": data_point.split(DATA_POINT_UNIT_SEPARATOR)[1]
                .lower()
                .strip(),
            }
            for data_point in data_points
            if DATA_POINT_UNIT_SEPARATOR in data_point
        }

        if rag_enabled == "true":
            with ThreadPoolExecutor(max_workers=RAG_WORKERS) as executor:
                future_to_data_point = {
                    executor.submit(
                        self._extract_relevant_chunks,
                        data_point,
                    ): data_point
                    for data_point in json_to_fill
                }

            for future in as_completed(future_to_data_point):
                data_point, context_for_llm, relevant_chunk_pages = future.result()
                relevant_docs.update(context_for_llm)
                context_json[data_point] = relevant_chunk_pages
        else:
            relevant_docs = [self.document_text]

        text_response = self._extract_data_points_using_llm(
            json_to_fill, " ".join(relevant_docs)
        )
        try:
            json_response = convert_text_to_dict(text_response)
            if conversion_map:
                json_response = convert_unit_of_measurement(
                    json_response, conversion_map
                )
            json_response["Chunks"] = context_json
            return json_response
        except Exception as e:
            logging.exception(
                f"JSON Conversion Failed! Reason: {e}. Converting with Regex."
            )
            return self._extract_json_with_regex(text_response, context_json)

    def _extract_separate_fields(self, data_points: List[str]) -> Dict[str, Any]:
        """
        Extracts separate fields.
        Args:
            data_points (List[str]): The data points.
        Returns:
            Dict[str, Any]: A dictionary containing the extracted data points.
        """
        context_json = {}
        final_response = {}
        for data_point in data_points:
            data_point = data_point.strip()
            if data_point:
                json_to_fill = {data_point: ""}
                relevant_chunks = self._get_relevant_docs_for_query(
                    data_point, k=NUMBER_OF_CHUNKS_FOR_LLM
                )
                (context_for_llm, relevant_chunk_pages) = self._prepare_chunks_for_llm(
                    relevant_chunks
                )
                context_json[data_point] = relevant_chunk_pages
                response = self._extract_data_points_using_llm(
                    json_to_fill, context_for_llm
                )
                try:
                    response = convert_text_to_dict(response)
                    final_response.update(response)
                except:
                    response = self._extract_json_with_regex(response)
                    if isinstance(response, dict):
                        final_response.update(response)
                    else:
                        final_response.update({data_point: response})
        final_response["Chunks"] = context_json
        return final_response

    def _get_prepared_chunks_for_llm(
        self, relevant_chunks: List[Tuple[Document, float]]
    ) -> Tuple[List, Dict[str, Any]]:
        """
        Prepares the chunks for the LLM.
        Args:
            relevant_chunks (List[Tuple[Document, float]]): The relevant chunks.
        Returns:
            Tuple[List, Dict[str, Any]]: A tuple containing the context for the LLM and the relevant chunk pages.
        """
        sorted_chunks = sorted(
            relevant_chunks, key=lambda chunk: chunk[0].metadata["child_chunk_num"]
        )
        context_for_llm = [doc.page_content for doc, _ in sorted_chunks]
        relevant_chunk_pages = {
            page: "\n\n".join(
                doc.page_content
                for doc, _ in sorted_chunks
                if doc.metadata["page"] == page
            )
            for page in {doc.metadata["page"] for doc, _ in sorted_chunks}
        }

        return context_for_llm, relevant_chunk_pages

    def _prepare_chunks_for_llm(
        self,
        relevant_chunks: List[Tuple[Document, float]],
        qa_call: bool = False,
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Prepares the chunks for the LLM.
        Args:
            relevant_chunks (List[Tuple[Document, float]]): The relevant chunks.
            qa_call (bool, optional): Whether the chunks are for a QA call. Default is False.
        Returns:
            Tuple[str, Dict[str, Any], set]: A tuple containing the context for the LLM, the relevant chunk pages, and the chunk numbers.
        """
        context_for_llm = ""
        relevant_chunk_pages = {}

        for doc, score in relevant_chunks:
            page_content = doc.page_content
            page = doc.metadata["page"]
            if page not in relevant_chunk_pages:
                relevant_chunk_pages[page] = page_content
            else:
                relevant_chunk_pages[page] = (
                    relevant_chunk_pages[page] + " \n\n " + page_content
                )
            context_for_llm += page_content + " "

        return context_for_llm, relevant_chunk_pages

    async def _get_relevant_docs_for_query_async(
        self, query: str, k: int = NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT
    ) -> List[Tuple[Document, float]]:
        """
        Gets the relevant chunks for the query.
        Args:
            query (str): The query.
            k (int, optional): The number of chunks to retrieve. Default is NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT.
        Returns:
            List[Tuple[Document, float]]: A list of tuples containing the relevant chunks and their scores.
        """
        logging.info(f"Embedding the User Query: {query}")
        query_embedding, usage = await self.faiss_obj.embed_user_query_async(
            query=query
        )
        self.request.state.token_tracker.update(
            {"total_embedding_tokens": usage["total_tokens"]}
        )
        self._update_endpoint_summary()
        logging.info(f"Fetching Chunks for: '{query}'")
        relevant_chunks = await self.faiss_obj.similarity_search_async(
            query_embedding, k=k
        )
        return relevant_chunks

    def _get_relevant_docs_for_query(
        self, query: str, k: int = NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT
    ) -> List[Tuple[Document, float]]:
        """
        Gets the relevant chunks for the query.
        Args:
            query (str): The query.
            k (int, optional): The number of chunks to retrieve. Default is NUMBER_OF_RETRIEVED_CHUNKS_PER_VARIANT.
        Returns:
            List[Tuple[Document, float]]: A list of tuples containing the relevant chunks and their scores.
        """
        logging.info(f"Embedding the User Query: {query}")
        query_embedding, usage = self.faiss_obj.embed_user_query(query=query)
        self.request.state.token_tracker.update(
            {"total_embedding_tokens": usage["total_tokens"]}
        )
        self._update_endpoint_summary()
        logging.info(f"Fetching Chunks for: '{query}'")
        relevant_chunks = self.faiss_obj.similarity_search(query_embedding, k=k)
        return relevant_chunks

    def _extract_data_points_using_llm(
        self, json_to_fill: Dict[str, Any], relevant_docs: str
    ) -> str:
        """
        Extracts data points using the LLM.
        Args:
            json_to_fill (Dict[str, Any]): The JSON to fill.
            relevant_docs (str): The relevant documents.
        Returns:
            str: The extracted data points.
        """
        query_prompt = RAG_QUERY_PROMPT.format(
            context=relevant_docs,
            question=RAG_JSON_PROMPT.format(json_to_fill=json_to_fill),
        )
        response = self.llm_object.get_llm_response_with_metadata(
            query_prompt,
            "json_object",
            EXTRACT_FIELDS_SYSTEM_PROMPT,
            self.llm_reasoning_enabled,
        )
        self.request.state.endpoint_summary.set_llm_model(self.llm_deployment_name)
        self._update_token_track(response)
        self._update_endpoint_summary()
        return response.get("content", "")

    def _update_token_track(self, response: Dict[str, Any]) -> None:
        """
        Updates the token usage.
        Args:
            response (Dict[str, Any]): The response from the LLM that contains the token usage.
        """
        self.request.state.token_tracker.update(
            {
                "total_tokens": response.get("total_tokens", 0),
                "prompt_tokens": response.get("prompt_tokens", 0),
                "completion_tokens": response.get("completion_tokens", 0),
                "total_cost": response.get("total_cost", 0),
            }
        )

    def _update_endpoint_summary(self) -> None:
        """
        Updates the endpoint summary.
        """
        usage = self.request.state.token_tracker.get_usage()
        update_endpoint_summary(
            self.request,
            total_word_count=usage["total_word_count"],
            total_tokens=usage["total_tokens"],
            prompt_tokens=usage["prompt_tokens"],
            completion_tokens=usage["completion_tokens"],
            total_embedding_tokens=usage["total_embedding_tokens"],
            total_cost=usage["total_cost"],
        )

    def _extract_json_with_regex(
        self, response: str, context_json: Optional[Dict[str, Any]] = None
    ) -> Union[Dict[str, Any], str]:
        """
        Extracts JSON with regex.
        Args:
            response (str): The response from the LLM.
            context_json (Dict[str, Any], optional): The context JSON. Default is None.
        Returns:
            Union[Dict[str, Any], str]: The extracted JSON or the response if no JSON is found.
        """
        line_re = re.compile(r"\{[^\}]+\}")
        records = line_re.findall(response)
        try:
            if not records:
                raise CustomError(
                    "Failed to create JSON response!",
                    status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
            final_dict = convert_text_to_dict(records[0])
            if context_json:
                final_dict["Chunks"] = context_json
            return final_dict
        except Exception as e:
            logging.exception(f"Not able to extract JSON with regex. Error: {e}")
            raise CustomError(
                f"Failed to create JSON response! Error: {e}",
                status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
