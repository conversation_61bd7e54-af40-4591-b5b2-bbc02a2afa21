from db import save_data
from models.endpoint_summary import EndpointSummary
from models.ocr_summary import OcrSummary
from sqlalchemy.orm import Session

async def save_ocr_summary(
    db : Session,
    ocr_summary : OcrSummary,
    ocr_type : str,
    total_pages : int,
    image_pages_count : int,
    text_pages_count : int,
) -> None:
    """
    Save the OCR summary.
    Args:
        db (Session): The database session.
        ocr_summary (OcrSummary): The OCR summary to be saved.
        ocr_type (str): The type of OCR used.
        total_pages (int): The total number of pages in the document.
        image_pages_count (int): The number of pages with images.
        text_pages_count (int): The number of pages with text.
    """
    
    ocr_summary.set_summary(ocr_type, total_pages, image_pages_count, text_pages_count)

    await save_data(db, ocr_summary)

async def save_endpoint_summary(
    db : Session,
    endpoint_summary : EndpointSummary,
) -> None:
    """
    Save the endpoint summary.
    Args:
        db (Session): The database session.
        endpoint_summary (EndpointSummary): The endpoint summary to be saved.
    """
    endpoint_summary.set_received_at()

    await save_data(db, endpoint_summary)