from exceptions import ValidationError
from typing import List
from utils.enums import Questions

async def validate_user_questions(
    questions_str: str = None,
    questions_list: Questions = None,
) -> List[str]:
    """
    Validate the questions and return the questions.
    Args:
        questions_str (str): The questions to validate.
        questions_list (Questions): The list of questions to validate.
    Returns:
        List[str]: The validated questions.
    """
    if questions_list and len(questions_list.questions) != 0:
        return questions_list.questions
    elif questions_str and questions_str.strip() != "":
        questions = questions_str.split(",")
        if len(questions) == 0:
            raise ValidationError("Questions must be provided either as a string or a list.")
        return questions
    else:
        raise ValidationError("Questions must be provided either as a string or a list.")