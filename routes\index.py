from fastapi import APIRouter
import logging
from constants import INSTANCE_TYPE

index_router = APIRouter()

@index_router.get("/")
async def index() -> dict:
    """
    Index route to check if the API is working.
    Returns:
        dict: The status of the API.
    """
    logging.info("Index is working")
    return {
        "Status": True,
        "message": "PDFPlus API is working..",
        "instance": INSTANCE_TYPE,
    }