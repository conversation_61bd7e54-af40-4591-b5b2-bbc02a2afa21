from langchain_community.vectorstores import Chroma
from chromadb.config import Settings
from AzureFunctions import getResponse
import logging
import re


CHROMA_SETTINGS = Settings(
    anonymized_telemetry=False,
    is_persistent=True,
)


def getEmbeddings(dbFolder, embedding_model):

    db = Chroma(
        persist_directory=dbFolder,
        embedding_function=embedding_model,
        client_settings=CHROMA_SETTINGS,
    )

    return db


def getFieldsTestingImproved(
    query, terms, questions, embedding_model, db_path, model_choice="af"
):

    VectorStore = getEmbeddings(db_path, embedding_model)

    if model_choice == "af":

        fields = terms.split(",")
        gpt_questions = questions.split(",")

        context_json = {}

        raw_doc = ""

        json_to_fill = {}
        gpt_question = ""

        for i in range(len(fields)):

            json_to_fill[query.split(",")[i].strip()] = ""
            field_doc = ""
            gpt_question += str(i + 1) + ". " + gpt_questions[i] + "\n"

            for doc in VectorStore.similarity_search(query=fields[i], k=2):
                pg_content = doc.page_content
                raw_doc += pg_content + " "
                field_doc += pg_content + " "

            # context_json[fields[i].strip()] = field_doc
            context_json[query.split(",")[i].strip()] = field_doc

        llm_query_prompt = f"""Strictly follow and carefully read the all the above provided Document, then read all the question carefully and then fill the following JSON according to answer found in the document. If any question is not found in the document, write '' in JSON. Do not calculate or make up any answer.

ONLY REPLY WITH JSON and don't write other text:
{json_to_fill}"""

        query_prompt = "Document: {" + raw_doc + "}\n\n"
        query_prompt = query_prompt + gpt_question + "\n\n" + llm_query_prompt

        # return context_json

        logging.info("Calling Azure API")

        response = getResponse(query_prompt).choices[0].message.content

        logging.debug("response: " + response)

        # response = response.replace("null","''")

        data = response

        line_re = re.compile(r"\{[^\}]+\}")
        records = line_re.findall(data)

        try:
            final_dict = eval(records[0])

            final_dict["Chunks"] = context_json
            final_dict["Prompt"] = query_prompt

            return final_dict
        except Exception as e:
            logging.info(str(e))
            return response

    else:

        fields = query.split(",")

        answers = []
        contexts = []
        context_json = {}

        for data_point in fields:

            json_to_fill = {}

            json_to_fill[data_point.strip()] = ""

            raw_doc = ""

            if len(data_point.strip()) > 0 and data_point != "":

                for doc in VectorStore.similarity_search(query=data_point, k=2):

                    raw_doc += doc.page_content + "\n\n"

                context_json[data_point.strip()] = raw_doc

                contexts.append(data_point + "-->" + str(raw_doc))

                llm_query_prompt = f"""Strictly follow and carefully read the above provided context to extract and fill the following JSON. If not found, fill '' in JSON.

ONLY REPLY WITH JSON and don't write other text:
{json_to_fill}"""

                query_prompt = "Context: {" + raw_doc + "}\n\n" + llm_query_prompt

                # return query_prompt

                logging.info("Calling Azure API")

                response = getResponse(query_prompt).choices[0].message.content

                logging.debug("Response---> " + response)

                try:
                    eval(str(response))
                    answers.append(response)
                except:
                    data = response

                    line_re = re.compile(r"\{[^\}]+\}")
                    records = line_re.findall(data)

                    try:
                        final_dict = eval(str(records[0]))

                        answers.append(final_dict)
                    except Exception as e:
                        logging.exception("Error while extracting records!")
                        logging.debug("response: " + response)
                        answers.append(response)

        final_response = {}

        for ind, i in enumerate(answers):

            try:
                final_response.update(eval(str(i)))
            except:
                final_response.update({fields[ind]: "JSON Formatting Error!"})
                pass

        final_response["Chunks"] = context_json

        return final_response


def getFieldsTesting(query, embedding_model, db_path, model_choice="af"):

    VectorStore = getEmbeddings(db_path, embedding_model)

    if model_choice == "af":

        fields = query.split(",")

        context_json = {}

        raw_doc = ""

        json_to_fill = {}

        for i in range(len(fields)):

            json_to_fill[fields[i].strip()] = ""
            field_doc = ""

            for doc in VectorStore.similarity_search(query=fields[i], k=2):
                pg_content = doc.page_content
                raw_doc += pg_content + " "
                field_doc += pg_content + " "

            context_json[fields[i].strip()] = field_doc

        llm_query_prompt = f"""Strictly follow and carefully read the above provided context to extract and fill the following JSON. If not found, write None in JSON.

ONLY REPLY WITH JSON and don't write other text:
{json_to_fill}"""

        query_prompt = "Context: {" + raw_doc + "}\n\n" + llm_query_prompt

        # return context_json

        logging.info("Calling Azure API")

        response = getResponse(query_prompt).choices[0].message.content

        logging.debug("response: " + response)

        # response = response.replace("null","''")

        data = response

        line_re = re.compile(r"\{[^\}]+\}")
        records = line_re.findall(data)

        try:
            final_dict = eval(records[0])

            final_dict["Chunks"] = context_json

            return final_dict
        except Exception as e:
            logging.exception("Error while extracting records!")
            return response

    else:

        fields = query.split(",")

        answers = []
        contexts = []
        context_json = {}

        for data_point in fields:

            json_to_fill = {}

            json_to_fill[data_point.strip()] = ""

            raw_doc = ""

            if len(data_point.strip()) > 0 and data_point != "":

                for doc in VectorStore.similarity_search(query=data_point, k=2):

                    raw_doc += doc.page_content + "\n\n"

                context_json[data_point.strip()] = raw_doc

                contexts.append(data_point + "-->" + str(raw_doc))

                llm_query_prompt = f"""Strictly follow and carefully read the above provided context to extract and fill the following JSON. If not found, fill '' in JSON.

ONLY REPLY WITH JSON and don't write other text:
{json_to_fill}"""

                query_prompt = "Context: {" + raw_doc + "}\n\n" + llm_query_prompt

                # return query_prompt

                logging.info("Calling Azure API")

                response = getResponse(query_prompt).choices[0].message.content

                logging.debug("Response---> " + response)

                try:
                    eval(str(response))
                    answers.append(response)
                except:
                    data = response

                    line_re = re.compile(r"\{[^\}]+\}")
                    records = line_re.findall(data)

                    try:
                        final_dict = eval(str(records[0]))

                        answers.append(final_dict)
                    except Exception as e:
                        logging.exception("Error while extracting records!")
                        logging.debug("response: " + response)
                        answers.append(response)

        final_response = {}

        for ind, i in enumerate(answers):

            try:
                final_response.update(eval(str(i)))
            except:
                final_response.update({fields[ind]: "JSON Formatting Error!"})
                pass

        final_response["Chunks"] = context_json

        return final_response
