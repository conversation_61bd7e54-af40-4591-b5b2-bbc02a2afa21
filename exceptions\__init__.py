from fastapi import HTTPException, status


class ValidationError(HTTPException):
    """Validation Error Class for Bad Request"""
    def __init__(self, message):
        super().__init__(status_code=status.HTTP_400_BAD_REQUEST, detail=message)


class UnauthorizedError(HTTPException):
    """Unauthorized Error Class for Unauthorized Access"""
    def __init__(self, message):
        super().__init__(status_code=status.HTTP_401_UNAUTHORIZED, detail=message)


class CustomError(HTTPException):
    """Custom Error Class for Custom Errors"""
    def __init__(self, message, status_code):
        super().__init__(status_code=status_code, detail=message)
