import logging
from fastapi import Request
from utils.enums import CloudPlatform
from utils.document_utils import calculate_pdf_word_count
from typing import List
from services.endpoint_handlers.rag_service import Embedding, FaissVectorStore
from utils.document_utils import SourceDocument
from models.endpoint_summary import update_endpoint_summary
from utils.cloud_utils import get_embedding_deployment_name


async def get_document_embeddings(
    request: Request,
    source_document: SourceDocument,
    embedding_platform: CloudPlatform,
) -> List[List[float]]:
    """
    Get the OCR and embeddings of the document.
    Args:
        request (Request): The request object.
        source_document (SourceDocument): The source document.
        embedding_platform (CloudPlatform): The type of embedding platform to use.
    Returns:
        dict: The embeddings of the document.
    """
    logging.info("Generate document embeddings.")

    vector_store = FaissVectorStore(
        request=request,
        embedding_platform=embedding_platform,
    )
    embeddings_data_with_tokens_usage = vector_store.create_document_embeddings(
        source_document=source_document,
    )

    update_endpoint_summary(
        request,
        total_word_count=calculate_pdf_word_count(source_document),
        total_tokens=0,
        prompt_tokens=0,
        completion_tokens=0,
        total_embedding_tokens=embeddings_data_with_tokens_usage["usage"][
            "total_tokens"
        ],
        total_cost=0,
    )

    return embeddings_data_with_tokens_usage["embedding_data"]


async def get_query_embeddings(
    request: Request,
    embedding_platform: CloudPlatform,
    questions: List[str],
) -> dict:
    """
    Get the OCR and embeddings of the document.
    Args:
        request (Request): The request object.
        embedding_platform (CloudPlatform): The type of embedding platform to use.
        query (str): The user query to be embedded.
    Returns:
        dict: The embeddings of the user question.
    """
    embedding = Embedding(embedding_platform)
    request.state.endpoint_summary.set_embedding_platform(
        embedding.embedding_object.cloud_name
    )
    request.state.endpoint_summary.set_embedding_model(
        get_embedding_deployment_name(embedding_platform)
    )
    embeddings_response = {}
    for question in questions:
        query_embedding, token_usage, _ = embedding.embed_query(question)
        embeddings_response[question] = query_embedding
        request.state.token_tracker.update(
            {"total_embedding_tokens": token_usage["total_tokens"]}
        )
        updated_token_usage = request.state.token_tracker.get_usage()
        update_endpoint_summary(
            request,
            total_word_count=updated_token_usage["total_word_count"],
            total_tokens=updated_token_usage["total_tokens"],
            prompt_tokens=updated_token_usage["prompt_tokens"],
            completion_tokens=updated_token_usage["completion_tokens"],
            total_embedding_tokens=updated_token_usage["total_embedding_tokens"],
            total_cost=updated_token_usage["total_cost"],
        )

    return embeddings_response
