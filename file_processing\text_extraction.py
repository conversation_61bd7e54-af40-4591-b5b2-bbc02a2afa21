import fitz
from io import Bytes<PERSON>
from constants import (
    LAMBDA_WORKERS,
    PADDLE_OCR_URL,
    AZURE_WORKERS,
    AWS_TEXTRACT_WORKERS,
    PDF_TO_IMAGE_WORKERS,
    DPI,
)
import logging
from difflib import Sequence<PERSON>atch<PERSON>
import numpy as np
import requests
from concurrent.futures import Thread<PERSON>oolExecutor, wait
import PIL.Image as Image
from utils import OCRType, convert_text_to_dict
import PyPDF2
import json
from file_processing.file_contents import get_file_contents
from exceptions import ValidationError
from db.db_operations import save_ocr_summary
from file_processing.text_sorting import get_sorted_text
from services.cloud_services.aws import AWSHelper
from services.cloud_services.azure import AzureHelper
from fastapi import Request, File
from typing import Union, Optional, List
import html2text
from utils.page_range_utils import filter_json_by_pages
from utils.cost_calculations_utils import calculate_ocr_cost
from models import OCRData


async def extract_annotations(file: BytesIO, pages_text: dict) -> dict:
    """
    Extract the annotations from the PDF file.
    Args:
        file (BytesIO): The PDF file.
        pages_text (dict): The pages text.
    Returns:
        dict: The pages text with the annotations.
    """
    reader = PyPDF2.PdfReader(stream=file)

    for page_num in range(len(reader.pages)):

        page_key = str(page_num + 1)
        if page_key not in pages_text:
            continue

        pages_text[page_key]["annotations"] = []

        page = reader.pages[page_num]
        media_box = page.mediabox

        page_size = {"width": int(media_box.width), "height": int(media_box.height)}

        if "/Annots" in page:
            for annot in page["/Annots"]:
                obj = annot.get_object()
                annotation = {}
                annotation["type"] = obj.get("/Subtype")
                annotation["bounding_box"] = obj.get("/Rect")
                annotation["image_size"] = page_size

                # Extracting content from different types of annotations
                if annotation["type"] == "/Link":
                    if "/A" in obj:
                        action = obj["/A"]
                        if "/URI" in action:
                            annotation["content"] = action["/URI"]
                        else:
                            annotation["content"] = str(action)
                elif annotation["type"] == "/Popup" and "/Contents" in obj:
                    annotation["content"] = obj["/Contents"]
                elif "/Contents" in obj:
                    annotation["content"] = obj["/Contents"]

                if annotation["type"]:
                    annotation["bounding_box"] = [
                        round(i) for i in annotation["bounding_box"]
                    ]
                    pages_text[str(page_num + 1)]["annotations"].append(annotation)

    return pages_text


def get_image_from_fitz_page(page: fitz.Page) -> Image.Image:
    """
    Get an image from a fitz page with dimensions that comply with Azure and AWS OCR requirements.
    Args:
        page (fitz.Page): The fitz page to get the image from.
    Returns:
        Image.Image: The pillow image from the fitz page.
    Note:
        Azure requires images to be between 50x50 and 10000x10000 pixels.
        AWS requires images to be maximum 10000x10000 pixels.
    """
    zoom = DPI / 72
    image = Image.open(
        BytesIO(page.get_pixmap(matrix=fitz.Matrix(zoom, zoom)).tobytes())
    )
    width, height = image.size
    logging.info(f"Original image size: {width}x{height}")
    min_dim, max_dim = 50, 10000
    if min(width, height) < min_dim or max(width, height) > max_dim:
        scale = min(max_dim / max(width, height), max(min_dim / min(width, height), 1))
        new_size = (int(width * scale), int(height * scale))
        logging.info(f"Resizing image to: {new_size[0]}x{new_size[1]}")
        image = image.resize(new_size, Image.Resampling.LANCZOS)
    return image


async def get_all_images_from_fitz_pages(
    fitz_pages: List[fitz.Page],
) -> List[Image.Image]:
    """
    Get all the images from the fitz pages.
    Args:
        fitz_pages (List[fitz.Page]): The fitz pages to get the images from.
    Returns:
        List[Image.Image]: The pillow images from the fitz pages.
    """
    with ThreadPoolExecutor(max_workers=PDF_TO_IMAGE_WORKERS) as executor:
        res = []
        for fitz_page in fitz_pages:
            res.append(executor.submit(get_image_from_fitz_page, fitz_page))
        completed, _ = wait(res)
        return [future.result() for future in sorted(completed, key=res.index)]


async def extract_text_and_prepare_ocr_images(
    file: BytesIO, pages_list: Optional[List] = None
) -> tuple[dict, List[Image.Image], List[int], int]:
    """
    Extract the text from text-based pages and prepare the OCR images.
    Args:
        file (BytesIO): The PDF file.
        pages_list (List, Optional): The list of pages to extract the text.
    Returns:
        tuple[dict, List[Image.Image], List[int], int]: The pages text, OCR pages, OCR page numbers, total pages.
    """
    fitz_pages_to_ocr = []
    ocr_page_numbers = []
    text_pages = {}

    with fitz.open(stream=file, filetype="pdf") as pdf:
        if pages_list:
            page_nums = pages_list
        else:
            page_nums = range(1, pdf.page_count + 1, 1)

        total_pages = len(page_nums)

        for page_num in page_nums:
            page = pdf.load_page(page_num - 1)
            page_num = str(page_num)
            words = page.get_text(option="words")
            bounding_boxes = []
            content = ""
            for word in words:
                x0 = int(word[0])
                y0 = int(word[1])
                x1 = int(word[2])
                y1 = int(word[3])
                word_text = word[4]
                bounding_boxes.append([[x0, y0, x1, y1], word_text])
                content += word_text + " "
            if content == "" or len(page.get_images(full=True)) > 0:
                fitz_pages_to_ocr.append(page)
                ocr_page_numbers.append(page_num)
            else:
                sorted_text = await get_sorted_text(bounding_boxes)
                text_pages[page_num] = {
                    "content": sorted_text,
                    "info": bounding_boxes,
                    "image_size": {
                        "width": int(page.rect.width),
                        "height": int(page.rect.height),
                    },
                    "angle": 0,
                    "extraction": "text",
                }
        ocr_pages = await get_all_images_from_fitz_pages(fitz_pages_to_ocr)

    return text_pages, ocr_pages, ocr_page_numbers, total_pages


async def extract_text_with_cloud_service(
    images: List[Image.Image],
    ocr_page_numbers: List[int],
    cloud_platform_obj: Union[AzureHelper, AWSHelper],
    WORKERS: int,
) -> dict:
    """
    Extract the text with the cloud service.
    Args:
        images (List[Image.Image]): The page images.
        ocr_page_numbers (List[int]): The OCR page numbers.
        cloud_platform_obj (Union[AzureHelper, AWSHelper]): The cloud platform object.
        WORKERS (int): The number of workers.
    Returns:
        dict: The OCR text JSON.
    """
    with ThreadPoolExecutor(max_workers=WORKERS) as executor:
        res = []
        for img, page_no in zip(images, ocr_page_numbers):
            img_byte_arr = BytesIO()
            img.save(img_byte_arr, format="PNG")
            res.append(
                executor.submit(
                    cloud_platform_obj.get_extracted_text_from_image,
                    image=img_byte_arr.getvalue(),
                    page_no=page_no,
                    image_width=img.size[0],
                    image_height=img.size[1],
                )
            )

    completed, _ = wait(res)
    document_text_list = [
        future.result() for future in sorted(completed, key=res.index)
    ]

    document_text_json = cloud_platform_obj.get_formatted_ocr_response(
        document_text_list
    )

    return document_text_json


def get_extracted_text_from_image_using_paddle(
    im_byte: bytes, page_no: int, image_width: int, image_height: int
) -> dict:
    """
    Get the extracted text from the image using PaddleOCR.
    Args:
        im_byte (bytes): The image byte array.
        page_no (int): The page number.
        image_width (int): The image width.
        image_height (int): The image height.
    Returns:
        dict: The OCR text JSON.
    """
    files = {"page": im_byte}

    logging.info(f"Extracting Page {page_no}....")

    try:
        response = requests.post(PADDLE_OCR_URL + "/extractText", files=files)
    except Exception as e:
        logging.exception("Error while calling Paddle OCR!")
        return str(e) + "," + str(page_no)

    try:
        paddle_json = convert_text_to_dict(response.text)
        paddle_json = convert_text_to_dict(paddle_json["body"])
        paddle_json["page"] = str(page_no)
        paddle_json["image_size"] = {"width": image_width, "height": image_height}

        return paddle_json

    except:
        return str(response.text) + "," + str(page_no)


async def extract_text_with_paddle_ocr(
    page_images: List[Image.Image], ocr_page_numbers: List[int]
) -> dict:
    """
    Extract the text with PaddleOCR.
    Args:
        page_images (List[Image.Image]): The page images.
        ocr_page_numbers (List[int]): The OCR page numbers.
    Returns:
        dict: The OCR text JSON.
    """
    with ThreadPoolExecutor(max_workers=LAMBDA_WORKERS) as executor:
        res = []

        for page_num, page_img in zip(ocr_page_numbers, page_images):
            image_width, image_height = page_img.size
            pil_im = Image.fromarray(np.array(page_img))
            b = BytesIO()
            pil_im.save(b, "jpeg")
            im_byte = b.getvalue()
            res.append(
                executor.submit(
                    get_extracted_text_from_image_using_paddle,
                    im_byte=im_byte,
                    page_no=page_num,
                    image_width=image_width,
                    image_height=image_height,
                )
            )

        completed, _ = wait(res)
        document_text_list = [
            future.result() for future in sorted(completed, key=res.index)
        ]

    document_text_json = {}
    for page in document_text_list:
        if type(page) == dict and "text" in list(page):
            raw_text = ""
            info_list = []
            for line in page["text"][0]:
                raw_text += line[1][0] + " "
                ll = [line[0][0][0], line[0][0][1], line[0][2][0], line[0][2][1]]
                info_list.append([ll, line[1][0]])
            document_text_json[page["page"]] = {}
            document_text_json[page["page"]]["content"] = raw_text
            document_text_json[page["page"]]["info"] = info_list
            document_text_json[page["page"]]["angle"] = 0
            document_text_json[page["page"]]["image_size"] = page["image_size"]
            document_text_json[page["page"]]["extraction"] = OCRType.Paddle
        else:
            logging.info(f"Error: {page}")
    return document_text_json


async def get_ocr_text_json(
    ocr_data: File,
    ocr_data_url: str,
    pdf_url: str,
    pdf: File,
    ocr_type: str,
    request: Request,
    pages_list: Optional[List] = None,
) -> dict:
    """
    Get the OCR text JSON.
    Args:
        ocr_data (File): The OCR data.
        ocr_data_url (str): The OCR data URL.
        pdf_url (str): The PDF URL.
        pdf (File): The PDF file.
        ocr_type (str): The OCR type.
        request (Request): The request object.
        pages_list (List, Optional): The list of pages to extract the text.
    Returns:
        dict: The OCR text JSON.
    """
    if ocr_data or ocr_data_url:
        json_file_contents = await get_file_contents("json", ocr_data_url, ocr_data)
        try:
            document_text_json = json.loads(json_file_contents)
            logging.info("OCR JSON received, ignoring the PDF OCR.")
            document_text_json = OCRData.validate_ocr_data(document_text_json)
            if pages_list:
                return await filter_json_by_pages(document_text_json, pages_list)
            document_text_json["usageCost"] = {"ocrCost": 0}
            return document_text_json
        except json.JSONDecodeError:
            raise ValidationError("Invalid OCR data format.")

    pdf_file_contents = await get_file_contents("pdf", pdf_url, pdf)
    logging.info("Extracting PDF text using OCR.")
    (document_text_json, total_pages, image_pages_count, text_pages_count) = (
        await process_and_extract_pdf_content(
            pdf_file_contents, ocr_type=ocr_type, pages_list=pages_list
        )
    )
    logging.info("PDF text extracted using OCR and Saving Summary to Database.")

    await save_ocr_summary(
        request.state.db,
        request.state.ocr_summary,
        ocr_type,
        total_pages,
        image_pages_count,
        text_pages_count,
    )

    ocr_cost = await calculate_ocr_cost(
        ocr_type=ocr_type, image_pages_count=image_pages_count
    )

    document_text_json["usageCost"] = {"ocrCost": ocr_cost}

    return document_text_json


async def process_and_extract_pdf_content(
    pdf_file_contents: bytes,
    ocr_type: str = OCRType.Azure,
    pages_list: Optional[List] = None,
) -> tuple[dict, int, int, int]:
    """
    Process and extract the PDF content.
    Args:
        pdf_file_contents (bytes): The PDF file contents.
        ocr_type (str): The OCR type.
        pages_list (List, Optional): The list of pages to extract the text.
    Returns:
        tuple[dict, int, int, int]: The pages text, total pages, image pages count, text pages count.
    """
    logging.info("Calling process_pdf_content.")

    file = BytesIO(pdf_file_contents)

    (pages_text, ocr_pages, ocr_page_numbers, total_pages) = (
        await extract_text_and_prepare_ocr_images(file, pages_list)
    )

    image_pages_count = len(ocr_pages)
    text_pages_count = len(pages_text)
    logging.info(f"Text Based Pages: {text_pages_count}")
    logging.info(f"Image Based Pages: {image_pages_count}")

    if image_pages_count != 0:

        logging.info(f"{image_pages_count} pages to be OCRed.")

        if ocr_type == OCRType.Paddle:
            logging.info("Using PaddleOCR.")
            ocr_pages_text = await extract_text_with_paddle_ocr(
                ocr_pages, ocr_page_numbers
            )
        elif ocr_type == OCRType.Azure:
            logging.info("Using Azure OCR.")
            ocr_pages_text = await extract_text_with_cloud_service(
                ocr_pages, ocr_page_numbers, AzureHelper(), AZURE_WORKERS
            )
        else:
            logging.info("Using AWS Textract OCR.")
            ocr_pages_text = await extract_text_with_cloud_service(
                ocr_pages, ocr_page_numbers, AWSHelper(), AWS_TEXTRACT_WORKERS
            )

        logging.info("OCR text extracted.")

        for key in ocr_pages_text:
            pages_text[str(key)] = ocr_pages_text[key]

    logging.info("Reading the annotations.")
    pages_text = await extract_annotations(file, pages_text)

    return pages_text, total_pages, image_pages_count, text_pages_count


async def get_html_text(html_file_content: bytes) -> str:
    """
    Get the text from the HTML file content.
    Args:
        html_file_content (bytes): The HTML file content.
    Returns:
        str: The text from the HTML file content.
    """
    return html2text.html2text(html_file_content.decode("utf-8"))


def find_demographic_section(document_text_json: dict) -> tuple[List[str], float]:
    """
    Find the demographic section of the document.
    Args:
        document_text_json (dict): The document text JSON.
    Returns:
        tuple[List[str], float]: The demographic pages, the score.
    """
    pages = {}

    for i in document_text_json:
        result = calculate_page_score(document_text_json[i].get("content", ""))
        pages[i] = result

    page_scores = sorted(pages.items(), key=lambda x: x[1], reverse=True)

    final_pages = [page_scores[0][0]]

    for i in page_scores[1:]:
        if page_scores[0][1] == i[1]:
            final_pages.append(i[0])
        else:
            break

    return final_pages, page_scores[0][1]


def calculate_page_score(text: str) -> float:
    """
    Calculate the page score.
    Args:
        text (str): The text of the page.
    Returns:
        float: The page score.
    """
    if text == "":
        return 0

    # keywords = ["Patient Information", "Patient Demographic", "Guarantor Information", "Patient Name", "Address", "Sex", "Gender"]

    keywords = {
        "Patient Information": 85,
        "Patient Demographic": 85,
        "Guarantor Information": 80,
        "Patient Name": 80,
        "Address": 85,
        "Sex": 100,
        "Age": 100,
        "Gender": 80,
        "MRN": 80,
        "SSN": 80,
        "Contact Information": 85,
    }

    similarity_ratios = []

    for keyword in keywords:

        similarity_score = partial_ratio(keyword, text)

        if similarity_score >= keywords[keyword]:
            similarity_ratios.append(1)

    return sum(similarity_ratios)


def partial_ratio(s1: str, s2: str) -> float:
    """
    Return the ratio of the most similar substring
    as a number between 0 and 100.
    Args:
        s1 (str): The first string.
        s2 (str): The second string.
    Returns:
        float: The similarity ratio.
    """

    if len(s1) <= len(s2):
        shorter = s1
        longer = s2
    else:
        shorter = s2
        longer = s1

    m = SequenceMatcher(None, shorter, longer, autojunk=False)
    blocks = m.get_matching_blocks()

    # each block represents a sequence of matching characters in a string
    # of the form (idx_1, idx_2, len)
    # the best partial match will block align with at least one of those blocks
    #   e.g. shorter = "abcd", longer = XXXbcdeEEE
    #   block = (1,3,3)
    #   best score === ratio("abcd", "Xbcd")
    scores = []
    for short_start, long_start, _ in blocks:
        new_long_start = max(0, long_start - short_start)
        new_long_end = new_long_start + len(shorter)
        long_substr = longer[new_long_start:new_long_end]

        m2 = SequenceMatcher(None, shorter, long_substr, autojunk=False)
        r = m2.ratio()
        if r > 0.995:
            return 100
        else:
            scores.append(r)

    return max(scores) * 100.0
