from utils.enums import Summary<PERSON>ethod, SummarizationPrompts
from fastapi import Request
from services.endpoint_handlers.summarization_service import Summarization
from utils.document_utils import SourceDocument


def summarize(
    request: Request,
    source_document: SourceDocument,
    system_template: str,
    summary_prompts: SummarizationPrompts,
    llm_platform: str,
    summary_method: SummaryMethod = SummaryMethod.MapReduce,
    llm_reasoning_enabled: str = "false",
) -> str:
    """
    Summarizes the document text.
    Args:
        request (Request): The request object.
        source_document: (SourceDocument): The document text or json.
        system_template (str): The system template for LLM.
        summary_prompts (PromptDetails): The summary prompts.
        llm_platform (str): The LLM platform.
        summary_method (SummaryMethod, optional): The summary method. Default is SummaryMethod.MapReduce.
        llm_reasoning_enabled (str): The enablement of LLM reasoning.
    Returns:
        str: The summary of the document.
    """
    summary_object = Summarization(
        request=request,
        source_document=source_document,
        system_template=system_template,
        summary_prompts=summary_prompts,
        llm_platform=llm_platform,
        summary_method=summary_method,
        llm_reasoning_enabled=llm_reasoning_enabled,
    )
    return summary_object.summarize()
