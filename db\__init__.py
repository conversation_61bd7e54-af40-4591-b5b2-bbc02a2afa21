from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from constants import DATABASE_URL, DISABLE_DATABASE_REPORTING
import logging
from models.endpoint_summary import EndpointSummary
from models.ocr_summary import OcrSummary
from typing import Generator, Union

engine = create_engine(DATABASE_URL)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db() -> Generator[Session, None, None]:
    """
    Get the database session.
    Returns:
        Session: The database session.
    """
    db: Session = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def save_data(db : Session, obj : Union[EndpointSummary, OcrSummary]) -> None:
    """
    Save the data to the database.
    Args:
        db (Session): The database session.
        obj (Union[EndpointSummary, OcrSummary]): The object to be saved.
    """
    if DISABLE_DATABASE_REPORTING != "true":
        try:
            db.add(obj)
            db.commit()
        except Exception as e:
            logging.exception(f"There was an error saving info to db! Error: {e}")
    else:
        logging.info("Database save disabled!")
