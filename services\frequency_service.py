from rapidfuzz import fuzz
import logging


def get_keyword_count(document_json: dict, query: str) -> dict:
    """
    Get the keyword count.
    Args:
        document_json (dict): The document text JSON.
        query (str): The query.
    Returns:
        dict: The keyword count.
    """
    keywords = query.split(",")
    logging.info(f"keywords: {keywords}")

    # Remove :,- from the keywords
    keywords = [
        keyword.strip().lower().translate(str.maketrans("", "", ":,-"))
        for keyword in keywords
    ]

    document_json = split_box_text_into_words(document_json)

    freqs = {}

    freqs["keywords"] = count_keyword_occurrences(document_json, keywords)

    return freqs


def count_keywords_in_page(
    keywords: list,
    data: list,
    page_num: int,
    keywords_freq: dict,
    page_data: dict,
    matching_threshold: int = 88,
) -> dict:
    """
    Count the keyword occurrences in a page.
    Args:
        keywords (list): The keywords.
        data (list): The data.
        page_num (int): The page number.
        keywords_freq (dict): The keyword frequency.
        page_data (dict): The page data.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        dict: The keyword frequency.
    """
    for keyword in keywords:
        # Create subsets for each of the keyword.
        subsets = create_subset_with_word_window(data, len(keyword.split()))
        for subset in subsets:
            subset[1] = subset[1].translate(str.maketrans("", "", ":,-"))
            similarity_score = fuzz.ratio(keyword, subset[1].strip())
            if similarity_score > matching_threshold:
                # If keyword not in dictionary.
                if keyword not in keywords_freq:
                    keywords_freq[keyword] = {
                        "Counts": 1,
                        "Pages": {
                            page_num: {
                                "Counts": 1,
                                "Boxes": [subset[0]],
                                "page_data": page_data,
                            }
                        },
                    }
                else:
                    # If keyword is in dictionary but on a different page.
                    if page_num not in keywords_freq[keyword]["Pages"]:
                        keywords_freq[keyword]["Counts"] += 1
                        keywords_freq[keyword]["Pages"][page_num] = {
                            "Counts": 1,
                            "Boxes": [subset[0]],
                            "page_data": page_data,
                        }

                    else:
                        # Check if the bounding box is the same as already added.
                        result = check_if_box_overlap(
                            keywords_freq[keyword]["Pages"][page_num], subset[0]
                        )
                        # print("Overlaped Result: ", result)
                        if result[0] == False:
                            keywords_freq[keyword]["Counts"] += 1
                            keywords_freq[keyword]["Pages"][page_num]["Counts"] += 1
                            keywords_freq[keyword]["Pages"][page_num]["Boxes"].append(
                                subset[0]
                            )
    return keywords_freq


def count_keyword_occurrences(
    document_json: dict, keywords: list, matching_threshold: int = 88
) -> dict:
    """
    Count the keyword occurrences.
    Args:
        document_json (dict): The document text JSON.
        keywords (list): The keywords.
        matching_threshold (int): The matching threshold. Default is 88.
    Returns:
        dict: The keyword count.
    """
    keywords_freq = {}
    for page_num in document_json:
        if "usageCost" in page_num:
            continue
        data = document_json[page_num]["info"]
        page_data = {
            "image_size": document_json[page_num]["image_size"],
            "angle": document_json[page_num]["angle"],
            "extraction": document_json[page_num]["extraction"],
        }
        keywords_freq = count_keywords_in_page(
            keywords, data, page_num, keywords_freq, page_data, matching_threshold
        )
    not_in_dict = [item for item in keywords if item not in keywords_freq]
    if len(not_in_dict) > 0:
        for kw in not_in_dict:
            keywords_freq[kw] = {"Counts": 0, "Pages": None}
    return keywords_freq


def check_if_box_overlap(subsets: list, subset: list) -> tuple:
    """
    Check if the subset overlaps with any of the subsets in the list.
    Args:
        subsets (list): The subsets.
        subset (list): The subset.
    Returns:
        tuple: The result, the smaller box, and the matched box.
    """
    res = False
    smaller_box = None
    matched_box = None
    for ss in subsets:
        if check_overlap(ss, subset) == True:
            smaller_box = pick_smaller_box(ss, subset)
            matched_box = ss
            res = True
            break
    return (res, smaller_box, matched_box)


def pick_smaller_box(box1: list, box2: list) -> list:
    """
    Pick the smaller box among two given bounding boxes.
    Args:
        box1 (list): List containing the coordinates of the first box in the format [x1, y1, x2, y2].
        box2 (list): List containing the coordinates of the second box in the format [x1, y1, x2, y2].

    Returns:
        list: The smaller box.
    """
    # Calculate the areas of the boxes
    area_box1 = (box1[2] - box1[0]) * (box1[3] - box1[1])
    area_box2 = (box2[2] - box2[0]) * (box2[3] - box2[1])

    # Compare the areas and return the smaller box
    if area_box1 < area_box2:
        return box1
    else:
        return box2


def check_overlap(box1: list, box2: list) -> bool:
    """
    Check if two boxes overlap.

    Args:
        box1 (list): List containing the coordinates of the first box in the format [x1, y1, x2, y2].
        box2 (list): List containing the coordinates of the second box in the format [x1, y1, x2, y2].

    Returns:
        bool: True if the boxes overlap, False otherwise.
    """

    return box1 == box2


def create_subset_with_word_window_without_boxes(
    raw_text: str, window_size: int
) -> list:
    """
    Create subsets with word window without boxes.
    Args:
        raw_text (str): The raw text.
        window_size (int): The window size.
    Returns:
        list: The subsets with word window.
    """
    subsets = []
    # Tokenize the text into words
    words = [i.lower() for i in raw_text.split(" ")]
    # Generate subsets of k words with a sliding window of 1
    subsets_of_k = [
        words[i : i + window_size] for i in range(len(words) - (window_size - 1))
    ]
    # Print the generated subsets
    for subset in subsets_of_k:
        subsets.append(" ".join(subset))
    return subsets


def create_subset_with_word_window(
    bounding_box_text_list: list, window_size: int
) -> list:
    """
    Create subsets with word window.
    Args:
        bounding_box_text_list (list): The bounding box text list.
        window_size (int): The window size.
    Returns:
        list: The subsets with word window.
    """
    subset_list = []
    for i in range(len(bounding_box_text_list) - window_size + 1):
        subset_text = " ".join(
            bounding_box_text_list[i][1] for i in range(i, i + window_size)
        ).strip()
        subset_box = [bounding_box_text_list[i][0] for i in range(i, i + window_size)]
        if window_size > 1:
            left_most = min(subset_box[0][0], subset_box[1][0])
            top_most = min(subset_box[0][1], subset_box[1][1])
            right_most = max(subset_box[0][2], subset_box[1][2])
            bottom_most = max(subset_box[0][3], subset_box[1][3])
            subset_list.append(
                [
                    [left_most, top_most, right_most, bottom_most],
                    subset_text.lower().strip(),
                ]
            )
        else:
            subset_list.append([subset_box[0], subset_text.lower().strip()])
    return subset_list


def split_box_text_into_words(document_json: dict) -> dict:
    """
    Split the box text into words.
    Args:
        document_json (dict): The JSON OCR.
    Returns:
        dict: The JSON OCR with the box text split into words.
    """
    for page_num in document_json:
        if "usageCost" in page_num:
            continue
        temp_tuples = []
        if document_json[page_num]["info"] != {}:
            for bb in document_json[page_num]["info"]:
                word_texts = [t for t in bb[1].strip().split(" ")]
                for w_t in word_texts:
                    temp_tuples.append((bb[0], w_t))
            document_json[page_num]["info"] = temp_tuples
    return document_json
